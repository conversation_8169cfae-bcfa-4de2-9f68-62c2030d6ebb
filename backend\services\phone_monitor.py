"""
Phone credentials monitor for FastPhone integration
"""
import logging
import re
import asyncio
import sys
import io
from contextlib import redirect_stdout, redirect_stderr
from typing import Optional, Tuple
from backend.services.phone_service import phone_service

logger = logging.getLogger(__name__)

class PhoneCredentialsMonitor:
    """Monitor for capturing FastPhone credentials from logs"""
    
    def __init__(self):
        self.phone_pattern = re.compile(r'Phone:\s*(\+?\d[\d\s\-\(\)]+)')
        self.access_code_pattern = re.compile(r'Access\s*[Cc]ode:\s*(\d+)')
        self.fastphone_pattern = re.compile(r'FastPhone.*?(\+\d[\d\s\-]+).*?(\d{6})')
        self.monitoring = False
        
    def extract_phone_credentials(self, text: str) -> Optional[Tuple[str, str]]:
        """Extract phone number and access code from text"""
        try:
            phone_match = self.phone_pattern.search(text)
            code_match = self.access_code_pattern.search(text)
            
            if phone_match and code_match:
                phone = phone_match.group(1).strip()
                code = code_match.group(1).strip()
                return phone, code
                
            # Try FastPhone pattern
            fastphone_match = self.fastphone_pattern.search(text)
            if fastphone_match:
                phone = fastphone_match.group(1).strip()
                code = fastphone_match.group(2).strip()
                return phone, code
                
            return None
            
        except Exception as e:
            logger.error(f"Error extracting phone credentials: {e}")
            return None
    
    def start_monitoring(self):
        """Start monitoring stdout/stderr for phone credentials"""
        if self.monitoring:
            return
            
        self.monitoring = True
        logger.info("Started monitoring for FastPhone credentials")
        
        # Capture stdout and stderr
        original_stdout = sys.stdout
        original_stderr = sys.stderr
        
        class CredentialsCapture:
            def __init__(self, original_stream, monitor):
                self.original_stream = original_stream
                self.monitor = monitor
                
            def write(self, text):
                # Write to original stream
                self.original_stream.write(text)
                
                # Check for phone credentials
                if text and isinstance(text, str):
                    credentials = self.monitor.extract_phone_credentials(text)
                    if credentials:
                        phone, code = credentials
                        logger.info(f"Captured phone credentials: {phone} (Code: {code})")
                        phone_service.update_phone_credentials(phone, code)
                        
            def flush(self):
                self.original_stream.flush()
                
            def __getattr__(self, name):
                return getattr(self.original_stream, name)
        
        # Replace stdout and stderr with capturing versions
        sys.stdout = CredentialsCapture(original_stdout, self)
        sys.stderr = CredentialsCapture(original_stderr, self)
        
    def stop_monitoring(self):
        """Stop monitoring"""
        self.monitoring = False
        logger.info("Stopped monitoring for FastPhone credentials")

# Global monitor instance
phone_monitor = PhoneCredentialsMonitor()

def start_phone_monitoring():
    """Start phone credentials monitoring"""
    phone_monitor.start_monitoring()

def stop_phone_monitoring():
    """Stop phone credentials monitoring"""
    phone_monitor.stop_monitoring()
