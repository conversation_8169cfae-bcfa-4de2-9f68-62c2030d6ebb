/**
 * Audio Visualization for AI Therapist
 */

function updateAudioLevel() {
    if (!analyser_input || !dataArray_input) return;

    analyser_input.getByteFrequencyData(dataArray_input);
    
    // Calculate average volume
    let sum = 0;
    for (let i = 0; i < dataArray_input.length; i++) {
        sum += dataArray_input[i];
    }
    const average = sum / dataArray_input.length;
    
    // Update pulse circle if it exists
    const pulseCircle = document.querySelector('.pulse-circle');
    if (pulseCircle) {
        const scale = 1 + (average / 255) * 0.5; // Scale between 1 and 1.5
        pulseCircle.style.setProperty('--audio-level', scale);
    }

    // Continue animation
    if (peerConnection && peerConnection.connectionState === 'connected') {
        requestAnimationFrame(updateAudioLevel);
    }
}

function updateVisualization() {
    if (!analyser || !dataArray || !boxContainer) return;

    analyser.getByteFrequencyData(dataArray);
    
    const boxes = boxContainer.querySelectorAll('.box');
    const bufferLength = analyser.frequencyBinCount;
    const step = Math.floor(bufferLength / boxes.length);

    boxes.forEach((box, index) => {
        const dataIndex = index * step;
        const value = dataArray[dataIndex];
        
        // Normalize the value (0-255) to a scale factor (0.1-1)
        const scale = 0.1 + (value / 255) * 0.9;
        
        // Apply transform
        box.style.transform = `scaleY(${scale})`;
        
        // Optional: Add color variation based on frequency
        const hue = (value / 255) * 60; // 0 to 60 degrees (red to yellow)
        box.style.background = `linear-gradient(135deg, hsl(${240 + hue}, 70%, 60%), hsl(${260 + hue}, 70%, 70%))`;
    });

    // Continue animation
    if (peerConnection && peerConnection.connectionState === 'connected') {
        requestAnimationFrame(updateVisualization);
    }
}

function resetVisualization() {
    if (!boxContainer) return;
    
    const boxes = boxContainer.querySelectorAll('.box');
    boxes.forEach(box => {
        box.style.transform = 'scaleY(0.1)';
        box.style.background = 'linear-gradient(135deg, var(--color-accent), #8b5cf6)';
    });
}
