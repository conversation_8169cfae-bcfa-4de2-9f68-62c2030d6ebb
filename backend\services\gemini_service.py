"""
Gemini AI service for AI Therapist
"""
import logging
from typing import Optional
import google.generativeai as genai
from backend.config.settings import get_settings

logger = logging.getLogger(__name__)

class GeminiService:
    """Service for interacting with Google Gemini AI"""
    
    def __init__(self):
        self.settings = get_settings()
        self.client = None
        self._initialize_client()
    
    def _initialize_client(self):
        """Initialize Gemini client"""
        try:
            genai.configure(api_key=self.settings.gemini_api_key)
            self.client = genai
            logger.info("Gemini client initialized")
        except Exception as e:
            logger.error(f"Failed to initialize Gemini client: {e}")
            raise
    
    async def generate_text_response(self, prompt: str, system_prompt: str) -> str:
        """Generate text response using Gemini"""
        try:
            model = self.client.GenerativeModel("gemini-2.0-flash-exp")
            response = await model.generate_content_async(
                f"System: {system_prompt}\n\nUser: {prompt}"
            )

            if hasattr(response, 'text') and response.text:
                return response.text
            else:
                return str(response)

        except Exception as e:
            logger.error(f"Error generating text response: {e}")
            raise
    
    def get_client(self):
        """Get the Gemini client instance"""
        return self.client
