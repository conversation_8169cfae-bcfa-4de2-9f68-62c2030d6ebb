<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Payment Success - AI Therapist</title>
    <link rel="stylesheet" href="/static/css/auth.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <style>
        .success-container {
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            padding: 1rem;
        }
        
        .success-card {
            background: white;
            border-radius: 1rem;
            padding: 3rem;
            text-align: center;
            max-width: 500px;
            width: 100%;
            box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1);
        }
        
        .success-icon {
            width: 80px;
            height: 80px;
            background: linear-gradient(135deg, #10b981, #059669);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 0 auto 2rem;
            animation: bounce 1s ease-in-out;
        }
        
        .success-icon i {
            font-size: 2rem;
            color: white;
        }
        
        @keyframes bounce {
            0%, 20%, 50%, 80%, 100% {
                transform: translateY(0);
            }
            40% {
                transform: translateY(-10px);
            }
            60% {
                transform: translateY(-5px);
            }
        }
        
        .success-title {
            font-size: 2rem;
            font-weight: 700;
            color: #1f2937;
            margin-bottom: 1rem;
        }
        
        .success-message {
            font-size: 1.1rem;
            color: #6b7280;
            margin-bottom: 2rem;
            line-height: 1.6;
        }
        
        .plan-info {
            background: #f9fafb;
            border-radius: 0.75rem;
            padding: 1.5rem;
            margin-bottom: 2rem;
            border: 1px solid #e5e7eb;
        }
        
        .plan-name {
            font-size: 1.2rem;
            font-weight: 600;
            color: #6366f1;
            margin-bottom: 0.5rem;
        }
        
        .plan-features {
            list-style: none;
            padding: 0;
            margin: 0;
        }
        
        .plan-features li {
            padding: 0.25rem 0;
            color: #4b5563;
            position: relative;
            padding-left: 1.5rem;
        }
        
        .plan-features li::before {
            content: '✓';
            position: absolute;
            left: 0;
            color: #10b981;
            font-weight: bold;
        }
        
        .action-buttons {
            display: flex;
            gap: 1rem;
            justify-content: center;
            flex-wrap: wrap;
        }
        
        .btn {
            padding: 0.75rem 2rem;
            border-radius: 0.75rem;
            text-decoration: none;
            font-weight: 600;
            font-size: 1rem;
            transition: all 0.3s ease;
            border: none;
            cursor: pointer;
            display: inline-flex;
            align-items: center;
            gap: 0.5rem;
        }
        
        .btn-primary {
            background: #6366f1;
            color: white;
        }
        
        .btn-primary:hover {
            background: #4f46e5;
            transform: translateY(-2px);
            box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1);
        }
        
        .btn-secondary {
            background: #f3f4f6;
            color: #374151;
            border: 1px solid #d1d5db;
        }
        
        .btn-secondary:hover {
            background: #e5e7eb;
        }
        
        .countdown {
            margin-top: 2rem;
            font-size: 0.9rem;
            color: #6b7280;
        }
        
        .countdown-number {
            font-weight: 600;
            color: #6366f1;
        }
        
        @media (max-width: 768px) {
            .success-card {
                padding: 2rem;
            }
            
            .success-title {
                font-size: 1.5rem;
            }
            
            .action-buttons {
                flex-direction: column;
            }
        }
    </style>
</head>
<body>
    <div class="success-container">
        <div class="success-card">
            <div class="success-icon">
                <i class="fas fa-check"></i>
            </div>
            
            <h1 class="success-title">Payment Successful!</h1>
            
            <p class="success-message">
                Thank you for subscribing to AI Therapist! Your payment has been processed successfully, 
                and you now have access to all the features of your selected plan.
            </p>
            
            <div class="plan-info" id="plan-info">
                <div class="plan-name" id="plan-name">Loading plan details...</div>
                <ul class="plan-features" id="plan-features">
                    <li>Loading features...</li>
                </ul>
            </div>
            
            <div class="action-buttons">
                <a href="/app" class="btn btn-primary">
                    <i class="fas fa-rocket"></i>
                    Start Using AI Therapist
                </a>
                <a href="/" class="btn btn-secondary">
                    <i class="fas fa-home"></i>
                    Back to Home
                </a>
            </div>
            
            <div class="countdown">
                Redirecting to your dashboard in <span class="countdown-number" id="countdown">10</span> seconds...
            </div>
        </div>
    </div>
    
    <script>
        // Get plan details from URL parameters
        const urlParams = new URLSearchParams(window.location.search);
        const sessionId = urlParams.get('session_id');
        const planType = urlParams.get('plan');
        
        // Load plan details
        async function loadPlanDetails() {
            try {
                const response = await fetch('/payments/plans');
                const data = await response.json();
                
                if (data.plans && planType && data.plans[planType]) {
                    const plan = data.plans[planType];
                    document.getElementById('plan-name').textContent = plan.name;
                    
                    const featuresContainer = document.getElementById('plan-features');
                    featuresContainer.innerHTML = '';
                    
                    plan.features.forEach(feature => {
                        const li = document.createElement('li');
                        li.textContent = feature;
                        featuresContainer.appendChild(li);
                    });
                } else {
                    document.getElementById('plan-name').textContent = 'Your Selected Plan';
                    document.getElementById('plan-features').innerHTML = '<li>All premium features unlocked!</li>';
                }
            } catch (error) {
                console.error('Error loading plan details:', error);
                document.getElementById('plan-name').textContent = 'Your Selected Plan';
                document.getElementById('plan-features').innerHTML = '<li>All premium features unlocked!</li>';
            }
        }
        
        // Countdown timer
        let countdown = 10;
        const countdownElement = document.getElementById('countdown');
        
        const timer = setInterval(() => {
            countdown--;
            countdownElement.textContent = countdown;
            
            if (countdown <= 0) {
                clearInterval(timer);
                window.location.href = '/app';
            }
        }, 1000);
        
        // Load plan details on page load
        document.addEventListener('DOMContentLoaded', function() {
            loadPlanDetails();
            
            // Add click handlers to stop countdown
            document.querySelectorAll('.btn').forEach(btn => {
                btn.addEventListener('click', () => {
                    clearInterval(timer);
                });
            });
        });
        
        // Show success animation
        setTimeout(() => {
            document.querySelector('.success-icon').style.transform = 'scale(1.1)';
            setTimeout(() => {
                document.querySelector('.success-icon').style.transform = 'scale(1)';
            }, 200);
        }, 500);
    </script>
</body>
</html>
