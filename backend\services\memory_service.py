"""
Memory service for AI Therapist to remember users and conversations
"""
import json
import logging
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Any
from sqlalchemy.orm import Session
from sqlalchemy import Column, Integer, String, Text, DateTime, ForeignKey, func
from sqlalchemy.ext.declarative import declarative_base

from backend.models.database import get_db, User

logger = logging.getLogger(__name__)

# Memory storage models
Base = declarative_base()

class UserMemory(Base):
    """Store user-specific memory information"""
    __tablename__ = "user_memories"
    
    id = Column(Integer, primary_key=True, index=True)
    user_id = Column(Integer, ForeignKey("users.id"), nullable=False)
    memory_type = Column(String(50), nullable=False)  # 'personal', 'preference', 'conversation'
    memory_key = Column(String(100), nullable=False)  # e.g., 'name', 'favorite_topic', 'last_mood'
    memory_value = Column(Text, nullable=False)
    confidence = Column(Integer, default=100)  # 0-100, how confident we are about this memory
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), server_default=func.now(), onupdate=func.now())

class ConversationHistory(Base):
    """Store conversation history for context"""
    __tablename__ = "conversation_history"
    
    id = Column(Integer, primary_key=True, index=True)
    user_id = Column(Integer, ForeignKey("users.id"), nullable=False)
    session_id = Column(String(100), nullable=False)
    message_type = Column(String(20), nullable=False)  # 'user' or 'ai'
    content = Column(Text, nullable=False)
    mode = Column(String(20), nullable=False)  # 'text', 'voice', 'video', 'phone'
    created_at = Column(DateTime(timezone=True), server_default=func.now())

class MemoryService:
    """Service for managing user memory and conversation history"""
    
    def __init__(self):
        self.logger = logging.getLogger(__name__)
    
    def get_user_context(self, user_id: int, db: Session) -> str:
        """Get personalized context for the user"""
        try:
            # Get user basic info
            user = db.query(User).filter(User.id == user_id).first()
            if not user:
                return ""
            
            context_parts = []
            
            # Add user's name
            if user.full_name:
                context_parts.append(f"The user's name is {user.full_name}.")
            elif user.username:
                context_parts.append(f"The user goes by {user.username}.")
            
            # Get user memories
            memories = db.query(UserMemory).filter(
                UserMemory.user_id == user_id,
                UserMemory.confidence >= 70  # Only include high-confidence memories
            ).order_by(UserMemory.updated_at.desc()).limit(10).all()
            
            if memories:
                memory_context = []
                for memory in memories:
                    if memory.memory_type == 'personal':
                        memory_context.append(f"Personal info: {memory.memory_key} = {memory.memory_value}")
                    elif memory.memory_type == 'preference':
                        memory_context.append(f"User preference: {memory.memory_key} = {memory.memory_value}")
                    elif memory.memory_type == 'conversation':
                        memory_context.append(f"Previous conversation: {memory.memory_key} = {memory.memory_value}")
                
                if memory_context:
                    context_parts.append("What you remember about this user:")
                    context_parts.extend(memory_context)
            
            # Get recent conversation context
            recent_conversations = db.query(ConversationHistory).filter(
                ConversationHistory.user_id == user_id,
                ConversationHistory.created_at >= datetime.now() - timedelta(hours=24)
            ).order_by(ConversationHistory.created_at.desc()).limit(5).all()
            
            if recent_conversations:
                context_parts.append("Recent conversation context:")
                for conv in reversed(recent_conversations):  # Show in chronological order
                    context_parts.append(f"{conv.message_type}: {conv.content[:100]}...")
            
            return "\n".join(context_parts)
            
        except Exception as e:
            self.logger.error(f"Error getting user context: {e}")
            return ""
    
    def store_memory(self, user_id: int, memory_type: str, memory_key: str, 
                    memory_value: str, confidence: int = 100, db: Session = None) -> bool:
        """Store a memory for the user"""
        try:
            if not db:
                db = next(get_db())
            
            # Check if memory already exists
            existing_memory = db.query(UserMemory).filter(
                UserMemory.user_id == user_id,
                UserMemory.memory_type == memory_type,
                UserMemory.memory_key == memory_key
            ).first()
            
            if existing_memory:
                # Update existing memory
                existing_memory.memory_value = memory_value
                existing_memory.confidence = confidence
                existing_memory.updated_at = datetime.now()
            else:
                # Create new memory
                new_memory = UserMemory(
                    user_id=user_id,
                    memory_type=memory_type,
                    memory_key=memory_key,
                    memory_value=memory_value,
                    confidence=confidence
                )
                db.add(new_memory)
            
            db.commit()
            self.logger.info(f"Stored memory for user {user_id}: {memory_key} = {memory_value}")
            return True
            
        except Exception as e:
            self.logger.error(f"Error storing memory: {e}")
            if db:
                db.rollback()
            return False
    
    def store_conversation(self, user_id: int, session_id: str, message_type: str,
                          content: str, mode: str, db: Session = None) -> bool:
        """Store conversation history"""
        try:
            if not db:
                db = next(get_db())
            
            conversation = ConversationHistory(
                user_id=user_id,
                session_id=session_id,
                message_type=message_type,
                content=content,
                mode=mode
            )
            
            db.add(conversation)
            db.commit()
            
            # Clean up old conversations (keep only last 100 per user)
            old_conversations = db.query(ConversationHistory).filter(
                ConversationHistory.user_id == user_id
            ).order_by(ConversationHistory.created_at.desc()).offset(100).all()
            
            for old_conv in old_conversations:
                db.delete(old_conv)
            
            db.commit()
            return True
            
        except Exception as e:
            self.logger.error(f"Error storing conversation: {e}")
            if db:
                db.rollback()
            return False
    
    def extract_memories_from_text(self, text: str, user_id: int, db: Session) -> List[Dict[str, Any]]:
        """Extract potential memories from user text"""
        memories = []
        text_lower = text.lower()
        
        # Extract name mentions
        name_patterns = [
            "my name is", "i'm called", "call me", "i am", "name's"
        ]
        for pattern in name_patterns:
            if pattern in text_lower:
                # Simple extraction - could be improved with NLP
                words = text.split()
                try:
                    pattern_index = next(i for i, word in enumerate(words) if pattern.replace(" ", "").lower() in word.lower())
                    if pattern_index + 1 < len(words):
                        potential_name = words[pattern_index + 1].strip(".,!?")
                        if potential_name.isalpha() and len(potential_name) > 1:
                            memories.append({
                                'type': 'personal',
                                'key': 'preferred_name',
                                'value': potential_name,
                                'confidence': 80
                            })
                except:
                    pass
        
        # Extract mood/feeling mentions
        mood_keywords = {
            'happy': ['happy', 'joy', 'excited', 'great', 'wonderful', 'amazing'],
            'sad': ['sad', 'depressed', 'down', 'upset', 'crying'],
            'anxious': ['anxious', 'worried', 'nervous', 'stressed', 'panic'],
            'angry': ['angry', 'mad', 'furious', 'irritated', 'frustrated']
        }
        
        for mood, keywords in mood_keywords.items():
            if any(keyword in text_lower for keyword in keywords):
                memories.append({
                    'type': 'conversation',
                    'key': 'recent_mood',
                    'value': mood,
                    'confidence': 70
                })
                break
        
        # Extract interests/hobbies
        hobby_keywords = ['love', 'enjoy', 'hobby', 'interested in', 'passionate about']
        for keyword in hobby_keywords:
            if keyword in text_lower:
                # Extract what comes after the keyword
                try:
                    start_index = text_lower.find(keyword) + len(keyword)
                    remaining_text = text[start_index:start_index + 50].strip()
                    if remaining_text:
                        memories.append({
                            'type': 'preference',
                            'key': 'interest',
                            'value': remaining_text,
                            'confidence': 60
                        })
                except:
                    pass
        
        return memories
    
    def process_user_message(self, user_id: int, message: str, session_id: str, 
                           mode: str, db: Session) -> str:
        """Process user message and extract memories, return enhanced context"""
        try:
            # Store the conversation
            self.store_conversation(user_id, session_id, 'user', message, mode, db)
            
            # Extract and store memories
            potential_memories = self.extract_memories_from_text(message, user_id, db)
            for memory in potential_memories:
                self.store_memory(
                    user_id, 
                    memory['type'], 
                    memory['key'], 
                    memory['value'], 
                    memory['confidence'], 
                    db
                )
            
            # Get enhanced context for AI
            context = self.get_user_context(user_id, db)
            return context
            
        except Exception as e:
            self.logger.error(f"Error processing user message: {e}")
            return ""
    
    def process_ai_response(self, user_id: int, response: str, session_id: str, 
                          mode: str, db: Session) -> bool:
        """Store AI response in conversation history"""
        try:
            return self.store_conversation(user_id, session_id, 'ai', response, mode, db)
        except Exception as e:
            self.logger.error(f"Error processing AI response: {e}")
            return False

# Global memory service instance
memory_service = MemoryService()
