# Gemini API Configuration (REQUIRED)
# Get your API key from: https://ai.google.dev/gemini-api/docs/api-key
GEMINI_API_KEY=your_gemini_api_key_here

# Google Cloud Configuration (Optional - for Vertex AI)
GOOGLE_CLOUD_PROJECT=your_google_cloud_project_id
USE_VERTEX_AI=false

# Server Configuration
PORT=8000
ENVIRONMENT=development

# Database Configuration (Optional)
DATABASE_URL=postgresql://user:password@localhost/ai_psychologist

# Security Configuration
SECRET_KEY=your_secret_key_here
JWT_SECRET_KEY=your_jwt_secret_here

# WebRTC Configuration
TURN_SERVER_URL=
TURN_USERNAME=
TURN_PASSWORD=

# Logging Configuration
LOG_LEVEL=INFO
LOG_FORMAT=json

# Rate Limiting
MAX_CONNECTIONS_PER_IP=10
MAX_REQUESTS_PER_MINUTE=60

# Audio Configuration
AUDIO_SAMPLE_RATE=24000
AUDIO_CHANNELS=1
AUDIO_FORMAT=PCM

# Crisis Detection Configuration
ENABLE_CRISIS_DETECTION=true
CRISIS_WEBHOOK_URL=

# Monitoring and Analytics
ENABLE_ANALYTICS=false
ANALYTICS_API_KEY=

# Development Features
DEBUG=false
ENABLE_DOCS=true
CORS_ORIGINS=*

# Production Optimizations
WORKERS=1
MAX_WORKER_CONNECTIONS=1000
KEEPALIVE_TIMEOUT=5
