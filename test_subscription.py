#!/usr/bin/env python3
"""Test script to verify subscription functionality"""

import requests
import json

BASE_URL = "http://localhost:8000"

def test_login_and_subscription():
    """Test login and subscription endpoints"""
    
    # Step 1: Login
    print("🔐 Testing login...")
    login_response = requests.post(f"{BASE_URL}/auth/login", json={
        "email": "<EMAIL>",
        "password": "123456"
    })
    
    if login_response.status_code != 200:
        print(f"❌ Login failed: {login_response.status_code}")
        print(f"   Error: {login_response.text}")
        return
    
    login_data = login_response.json()
    token = login_data['access_token']
    print(f"✅ Login successful!")
    print(f"   User: {login_data['user']['email']}")
    print(f"   Token: {token[:20]}...")
    
    # Step 2: Test plans endpoint
    print("\n📋 Testing plans endpoint...")
    plans_response = requests.get(f"{BASE_URL}/payments/plans")
    
    if plans_response.status_code == 200:
        plans_data = plans_response.json()
        print(f"✅ Plans loaded successfully!")
        print(f"   Available plans: {list(plans_data['plans'].keys())}")
        for plan_name, plan_info in plans_data['plans'].items():
            print(f"   - {plan_name}: {plan_info['name']} (${plan_info['price_cents']/100}/month)")
    else:
        print(f"❌ Plans failed: {plans_response.status_code}")
        print(f"   Error: {plans_response.text}")
    
    # Step 3: Test usage endpoint
    print("\n📊 Testing usage endpoint...")
    headers = {"Authorization": f"Bearer {token}"}
    usage_response = requests.get(f"{BASE_URL}/payments/usage", headers=headers)
    
    if usage_response.status_code == 200:
        usage_data = usage_response.json()
        print(f"✅ Usage data loaded successfully!")
        print(f"   Plan: {usage_data['plan_type']}")
        print(f"   Minutes: {usage_data['minutes_used']}/{usage_data['minutes_limit']}")
        print(f"   Phone calls: {usage_data['phone_calls_used']}/{usage_data['phone_calls_limit']}")
    else:
        print(f"❌ Usage failed: {usage_response.status_code}")
        print(f"   Error: {usage_response.text}")
    
    # Step 4: Test WebSocket authentication
    print("\n🔌 Testing WebSocket authentication...")
    try:
        import websocket
        
        def on_message(ws, message):
            print(f"   WebSocket message: {message}")
            if message == "AUTH_SUCCESS":
                print("   ✅ WebSocket authentication successful!")
                ws.send("Hello, this is a test message")
            elif message == "AUTH_FAILED":
                print("   ❌ WebSocket authentication failed!")
            else:
                print(f"   🤖 AI Response: {message[:100]}...")
            ws.close()
        
        def on_error(ws, error):
            print(f"   ❌ WebSocket error: {error}")
        
        def on_open(ws):
            print("   🔌 WebSocket connected, sending auth...")
            ws.send(f"AUTH:{token}")
        
        ws = websocket.WebSocketApp(f"ws://localhost:8000/ws",
                                  on_message=on_message,
                                  on_error=on_error,
                                  on_open=on_open)
        
        ws.run_forever()
        
    except ImportError:
        print("   ⚠️ websocket-client not installed, skipping WebSocket test")
    except Exception as e:
        print(f"   ❌ WebSocket test failed: {e}")

if __name__ == "__main__":
    test_login_and_subscription()
