"""
System routes for AI Therapist (health, debug, voices)
"""
import sys
import logging
from datetime import datetime
from fastapi import APIRouter, HTTPException
from backend.models.schemas import HealthResponse, VoicesResponse, VoiceInfo
from backend.config.settings import get_settings, current_dir
from backend.services.webrtc_service import is_webrtc_available, get_webrtc_error
from backend.utils.session_manager import get_all_sessions

logger = logging.getLogger(__name__)
router = APIRouter()

@router.get("/api/voices", response_model=VoicesResponse)
async def get_available_voices():
    """Get list of available Gemini voices"""
    voices = [
        VoiceInfo(id="Puck", name="Puck", description="Warm, conversational voice"),
        VoiceInfo(id="Charon", name="Charon", description="Calm, reassuring voice"),
        VoiceInfo(id="Kore", name="Kore", description="Gentle, empathetic voice"),
        VoiceInfo(id="<PERSON>rir", name="<PERSON><PERSON><PERSON>", description="Strong, supportive voice"),
        VoiceInfo(id="Aoede", name="Aoede", description="Melodic, soothing voice (recommended)"),
    ]
    
    return VoicesResponse(
        voices=voices,
        fastrtc_available=is_webrtc_available()
    )

@router.get("/health", response_model=HealthResponse)
async def health_check():
    """Health check endpoint for Railway deployment"""
    try:
        active_sessions = get_all_sessions()
        
        return HealthResponse(
            status="healthy",
            timestamp=datetime.utcnow().isoformat(),
            service="ai-therapist-gemini",
            version="2.1.0",
            fastrtc_available=is_webrtc_available(),
            video_support=is_webrtc_available(),
            active_sessions=len(active_sessions)
        )
    except Exception as e:
        logger.error(f"Health check failed: {e}")
        raise HTTPException(status_code=503, detail="Service unhealthy")

@router.get("/debug")
async def debug_info():
    """Debug endpoint to check file existence and configuration"""
    try:
        settings = get_settings()
        html_file = current_dir / "index.html"
        static_dir = current_dir / "static"
        frontend_dir = current_dir / "frontend"
        
        return {
            "current_directory": str(current_dir),
            "html_file_exists": html_file.exists(),
            "html_file_path": str(html_file),
            "static_dir_exists": static_dir.exists(),
            "frontend_dir_exists": frontend_dir.exists(),
            "gemini_api_key_set": bool(settings.gemini_api_key),
            "fastrtc_available": is_webrtc_available(),
            "fastrtc_error": get_webrtc_error(),
            "video_support": is_webrtc_available(),
            "active_sessions": list(get_all_sessions().keys()),
            "files_in_directory": [f.name for f in current_dir.iterdir() if f.is_file()][:10],
            "python_version": sys.version,
            "backend_structure": {
                "config": (current_dir / "backend" / "config").exists(),
                "models": (current_dir / "backend" / "models").exists(),
                "routes": (current_dir / "backend" / "routes").exists(),
                "handlers": (current_dir / "backend" / "handlers").exists(),
                "services": (current_dir / "backend" / "services").exists(),
                "utils": (current_dir / "backend" / "utils").exists(),
            },
            "frontend_structure": {
                "css": (current_dir / "frontend" / "css").exists(),
                "js": (current_dir / "frontend" / "js").exists(),
                "assets": (current_dir / "frontend" / "assets").exists(),
            },
            "recommendations": [
                "Text chat is always available",
                "Voice and video chat require FastRTC compatibility",
                f"Current issue: {get_webrtc_error()}" if not is_webrtc_available() else "No issues detected"
            ]
        }
    except Exception as e:
        logger.error(f"Debug info error: {e}")
        raise HTTPException(status_code=500, detail=f"Debug info failed: {str(e)}")
