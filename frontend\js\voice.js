/**
 * Voice Chat Functionality for AI Therapist
 */

function setupVoiceControls() {
    if (!startButton) return;

    startButton.addEventListener('click', () => {
        if (peerConnection && peerConnection.connectionState === 'connected') {
            stopWebRTC();
        } else {
            setupWebRTC();
        }
    });
}

function updateButtonState() {
    if (!startButton) return;

    if (peerConnection && (peerConnection.connectionState === 'connecting' || peerConnection.connectionState === 'new')) {
        startButton.innerHTML = `
            <div class="spinner"></div>
            <span>Connecting to AI Therapist...</span>
        `;
        startButton.disabled = true;
        startButton.classList.remove('danger');
    } else if (peerConnection && peerConnection.connectionState === 'connected') {
        const pulseContainer = document.createElement('div');
        pulseContainer.className = 'pulse-container';
        pulseContainer.innerHTML = `
            <div class="pulse-circle"></div>
            <span>🔊 End Voice Session</span>
        `;

        const muteToggle = document.createElement('div');
        muteToggle.className = 'mute-toggle';
        muteToggle.title = isMuted ? 'Unmute' : 'Mute';
        muteToggle.innerHTML = isMuted ? micMutedIconSVG : micIconSVG;
        muteToggle.addEventListener('click', toggleMute);

        startButton.innerHTML = '';
        startButton.appendChild(pulseContainer);
        startButton.appendChild(muteToggle);
        startButton.disabled = false;
        startButton.classList.add('danger');
    } else {
        startButton.innerHTML = '🎤 Start Voice Session';
        startButton.disabled = false;
        startButton.classList.remove('danger');
    }
}

function toggleMute(event) {
    event.stopPropagation();
    if (!peerConnection || peerConnection.connectionState !== 'connected') return;

    isMuted = !isMuted;
    console.log("Mute toggled:", isMuted);

    peerConnection.getSenders().forEach(sender => {
        if (sender.track && sender.track.kind === 'audio') {
            sender.track.enabled = !isMuted;
            console.log(`Audio track ${sender.track.id} enabled: ${!isMuted}`);
        }
    });

    updateButtonState();
}

// Audio-only WebRTC setup
async function setupWebRTC() {
    const config = RTC_CONFIGURATION;
    peerConnection = new RTCPeerConnection(config);
    webrtc_id = Math.random().toString(36).substring(7);

    const timeoutId = setTimeout(() => {
        showToast("Connection is taking longer than usual. Please check your internet connection.", 'warning');
    }, 20000);

    try {
        const stream = await navigator.mediaDevices.getUserMedia({ 
            audio: {
                echoCancellation: true,
                noiseSuppression: true,
                autoGainControl: true,
                sampleRate: 16000
            } 
        });
        
        stream.getTracks().forEach(track => peerConnection.addTrack(track, stream));

        if (!audioContext || audioContext.state === 'closed') {
            audioContext = new AudioContext({ sampleRate: 24000 });
        }
        
        if (source_input) {
            try { source_input.disconnect(); } catch (e) { console.warn("Error disconnecting previous input source:", e); }
            source_input = null;
        }
        
        source_input = audioContext.createMediaStreamSource(stream);
        analyser_input = audioContext.createAnalyser();
        source_input.connect(analyser_input);
        analyser_input.fftSize = 64;
        dataArray_input = new Uint8Array(analyser_input.frequencyBinCount);
        updateAudioLevel();

        peerConnection.addEventListener('connectionstatechange', () => {
            console.log('Voice connection state:', peerConnection.connectionState);
            if (peerConnection.connectionState === 'connected') {
                clearTimeout(timeoutId);
                showToast('Connected to AI Therapist Voice!', 'success');
                showStatus('Voice session active - say "bye" to end');
                if (analyser_input) updateAudioLevel();
                if (analyser) updateVisualization();
            } else if (['disconnected', 'failed', 'closed'].includes(peerConnection.connectionState)) {
                showStatus('Voice session ended');
                endSession();
            }
            updateButtonState();
        });

        peerConnection.onicecandidate = ({ candidate }) => {
            if (candidate) {
                console.debug("Sending ICE candidate", candidate);
                fetch('/audio/webrtc/offer', {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify({
                        candidate: candidate.toJSON(),
                        webrtc_id: webrtc_id,
                        type: "ice-candidate",
                    })
                });
            }
        };

        peerConnection.addEventListener('track', (evt) => {
            if (evt.track.kind === 'audio' && audioOutput) {
                if (audioOutput.srcObject !== evt.streams[0]) {
                    audioOutput.srcObject = evt.streams[0];
                    audioOutput.play().catch(e => console.error("Audio play failed:", e));

                    if (!audioContext || audioContext.state === 'closed') {
                        console.warn("AudioContext not ready for output track analysis.");
                        return;
                    }
                    
                    if (source_output) {
                        try { source_output.disconnect(); } catch (e) { console.warn("Error disconnecting previous output source:", e); }
                        source_output = null;
                    }
                    
                    source_output = audioContext.createMediaStreamSource(evt.streams[0]);
                    analyser = audioContext.createAnalyser();
                    source_output.connect(analyser);
                    analyser.fftSize = 2048;
                    dataArray = new Uint8Array(analyser.frequencyBinCount);
                    updateVisualization();
                }
            }
        });

        dataChannel = peerConnection.createDataChannel('control');
        dataChannel.onmessage = (event) => {
            const eventJson = JSON.parse(event.data);
            if (eventJson.type === "error") {
                showToast(eventJson.message, 'error');
            } else if (eventJson.type === "send_input") {
                fetch('/input_hook', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        webrtc_id: webrtc_id,
                        voice_name: voiceSelect ? voiceSelect.value : 'Aoede',
                        mode: "audio"
                    })
                });
            }
        };

        const offer = await peerConnection.createOffer();
        await peerConnection.setLocalDescription(offer);

        const response = await fetch('/audio/webrtc/offer', {
            method: 'POST',
            headers: { 'Content-Type': 'application/json' },
            body: JSON.stringify({
                sdp: peerConnection.localDescription.sdp,
                type: peerConnection.localDescription.type,
                webrtc_id: webrtc_id,
            })
        });

        const serverResponse = await response.json();

        if (serverResponse.status === 'failed') {
            const errorMsg = serverResponse.meta.error === 'concurrency_limit_reached'
                ? `Too many active sessions. Maximum limit is ${serverResponse.meta.limit}. Please try again later.`
                : `Connection failed: ${serverResponse.meta.error}`;
            showToast(errorMsg, 'error');
            stopWebRTC();
            return;
        }

        await peerConnection.setRemoteDescription(serverResponse);

    } catch (err) {
        clearTimeout(timeoutId);
        console.error('Error setting up Voice WebRTC:', err);
        showToast('Failed to establish voice connection. Please check your microphone permissions and try again.', 'error');
        stopWebRTC();
    }
}

function stopWebRTC() {
    if (peerConnection) {
        peerConnection.close();
        peerConnection = null;
    }
    
    if (localStream) {
        localStream.getTracks().forEach(track => track.stop());
        localStream = null;
    }
    
    updateButtonState();
}

function endSession() {
    stopWebRTC();
    showStatus('Session ended');
}
