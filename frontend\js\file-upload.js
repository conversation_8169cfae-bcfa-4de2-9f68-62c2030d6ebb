/**
 * File Upload Functionality for AI Therapist
 */

function setupFileUpload() {
    if (!uploadZone || !fileInput || !uploadedFilesContainer) return;

    uploadZone.addEventListener('click', () => {
        fileInput.click();
    });

    uploadZone.addEventListener('dragover', (e) => {
        e.preventDefault();
        uploadZone.classList.add('dragover');
    });

    uploadZone.addEventListener('dragleave', () => {
        uploadZone.classList.remove('dragover');
    });

    uploadZone.addEventListener('drop', (e) => {
        e.preventDefault();
        uploadZone.classList.remove('dragover');
        const files = e.dataTransfer.files;
        handleFiles(files);
    });

    fileInput.addEventListener('change', (e) => {
        const files = e.target.files;
        handleFiles(files);
    });
}

async function handleFiles(files) {
    for (let file of files) {
        try {
            const formData = new FormData();
            formData.append('file', file);

            const response = await fetch('/upload_file', {
                method: 'POST',
                body: formData
            });

            if (response.ok) {
                const result = await response.json();
                uploadedFiles.push(result.encoded_data);
                displayUploadedFile(result.filename, result.size);
                showToast(`File "${result.filename}" uploaded successfully!`, 'success');
            } else {
                throw new Error('Upload failed');
            }
        } catch (error) {
            console.error('File upload error:', error);
            showToast(`Failed to upload "${file.name}"`, 'error');
        }
    }
}

function displayUploadedFile(filename, size) {
    if (!uploadedFilesContainer) return;
    
    const fileDiv = document.createElement('div');
    fileDiv.className = 'uploaded-file';
    fileDiv.innerHTML = `
        <span>📄 ${filename} (${Math.round(size / 1024)}KB)</span>
        <button class="file-remove" onclick="removeFile(this, '${filename}')">×</button>
    `;
    uploadedFilesContainer.appendChild(fileDiv);
}

function removeFile(button, filename) {
    // Remove from uploaded files array
    uploadedFiles = uploadedFiles.filter(file => !file.data || file.filename !== filename);
    // Remove from UI
    button.parentElement.remove();
    showToast(`Removed "${filename}"`, 'warning');
}
