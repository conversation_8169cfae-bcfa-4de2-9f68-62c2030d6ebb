"""
Stripe payment service for AI Therapist
"""
import os
import stripe
import logging
from typing import Dict, Any, Optional
from sqlalchemy.orm import Session
from fastapi import HTTPException, status
from backend.models.database import User, Subscription, PaymentHistory, SUBSCRIPTION_PLANS
from datetime import datetime, timedelta

logger = logging.getLogger(__name__)

# Stripe configuration
stripe.api_key = os.getenv("STRIPE_SECRET_KEY")
STRIPE_PUBLISHABLE_KEY = os.getenv("STRIPE_PUBLISHABLE_KEY")
STRIPE_WEBHOOK_SECRET = os.getenv("STRIPE_WEBHOOK_SECRET")

# Make STRIPE_PUBLISHABLE_KEY available as class attribute
class StripeService:
    STRIPE_PUBLISHABLE_KEY = STRIPE_PUBLISHABLE_KEY

    """Stripe payment processing service"""

    @staticmethod
    def create_customer(user: User) -> str:
        """Create a Stripe customer for a user"""
        try:
            customer = stripe.Customer.create(
                email=user.email,
                name=user.full_name,
                metadata={
                    "user_id": str(user.id),
                    "username": user.username
                }
            )
            logger.info(f"Stripe customer created: {customer.id} for user {user.email}")
            return customer.id
        except stripe.error.StripeError as e:
            logger.error(f"Error creating Stripe customer: {e}")
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail="Failed to create payment profile"
            )
    
    @staticmethod
    def create_checkout_session(
        db: Session,
        user: User,
        plan_type: str,
        success_url: str,
        cancel_url: str
    ) -> Dict[str, Any]:
        """Create a Stripe checkout session for subscription"""
        if plan_type not in SUBSCRIPTION_PLANS:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="Invalid plan type"
            )
        
        plan = SUBSCRIPTION_PLANS[plan_type]
        
        if plan_type == "free":
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="Free plan doesn't require payment"
            )
        
        try:
            # Create or get Stripe customer
            if not user.stripe_customer_id:
                customer_id = StripeService.create_customer(user)
                user.stripe_customer_id = customer_id
                db.commit()
            else:
                customer_id = user.stripe_customer_id
            
            # Create checkout session
            session = stripe.checkout.Session.create(
                customer=customer_id,
                payment_method_types=['card'],
                line_items=[{
                    'price': plan["stripe_price_id"],
                    'quantity': 1,
                }],
                mode='subscription',
                success_url=success_url + "?session_id={CHECKOUT_SESSION_ID}",
                cancel_url=cancel_url,
                metadata={
                    "user_id": str(user.id),
                    "plan_type": plan_type
                }
            )
            
            logger.info(f"Checkout session created: {session.id} for user {user.email}")
            return {
                "checkout_url": session.url,
                "session_id": session.id
            }
            
        except stripe.error.StripeError as e:
            logger.error(f"Error creating checkout session: {e}")
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail="Failed to create payment session"
            )
    
    @staticmethod
    def create_portal_session(user: User, return_url: str) -> str:
        """Create a Stripe customer portal session"""
        if not user.stripe_customer_id:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="No payment profile found"
            )
        
        try:
            session = stripe.billing_portal.Session.create(
                customer=user.stripe_customer_id,
                return_url=return_url
            )
            
            logger.info(f"Portal session created for user {user.email}")
            return session.url
            
        except stripe.error.StripeError as e:
            logger.error(f"Error creating portal session: {e}")
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail="Failed to create billing portal session"
            )
    
    @staticmethod
    def handle_checkout_completed(db: Session, session_data: Dict[str, Any]) -> bool:
        """Handle successful checkout completion"""
        try:
            user_id = int(session_data["metadata"]["user_id"])
            plan_type = session_data["metadata"]["plan_type"]
            
            user = db.query(User).filter(User.id == user_id).first()
            if not user:
                logger.error(f"User not found for checkout: {user_id}")
                return False
            
            # Get subscription details from Stripe
            subscription_id = session_data["subscription"]
            stripe_subscription = stripe.Subscription.retrieve(subscription_id)
            
            # Update user subscription
            subscription = db.query(Subscription).filter(Subscription.user_id == user_id).first()
            if not subscription:
                subscription = Subscription(user_id=user_id)
                db.add(subscription)
            
            plan = SUBSCRIPTION_PLANS[plan_type]
            subscription.plan_type = plan_type
            subscription.status = "active"
            subscription.stripe_subscription_id = subscription_id
            subscription.stripe_price_id = plan["stripe_price_id"]
            subscription.current_period_start = datetime.fromtimestamp(stripe_subscription.current_period_start)
            subscription.current_period_end = datetime.fromtimestamp(stripe_subscription.current_period_end)
            subscription.monthly_minutes_limit = plan["monthly_minutes"]
            subscription.phone_calls_limit = plan["phone_calls"]
            subscription.monthly_minutes_used = 0  # Reset usage
            subscription.phone_calls_used = 0  # Reset usage
            
            # Create payment history record
            payment_history = PaymentHistory(
                user_id=user_id,
                stripe_payment_intent_id=session_data.get("payment_intent"),
                amount_cents=session_data["amount_total"],
                currency=session_data["currency"],
                status="succeeded",
                plan_type=plan_type,
                billing_period_start=subscription.current_period_start,
                billing_period_end=subscription.current_period_end
            )
            
            db.add(payment_history)
            db.commit()

            # Send subscription confirmation email
            try:
                from backend.services.email_service import email_service
                email_service.send_subscription_email(
                    user_email=user.email,
                    user_name=user.full_name or user.username,
                    plan_name=plan["name"],
                    plan_features=plan["features"]
                )
                logger.info(f"Subscription email sent to {user.email}")
            except Exception as email_error:
                logger.error(f"Failed to send subscription email: {email_error}")
                # Don't fail the subscription if email fails

            logger.info(f"Subscription updated for user {user.email}: {plan_type}")
            return True
            
        except Exception as e:
            logger.error(f"Error handling checkout completion: {e}")
            db.rollback()
            return False
    
    @staticmethod
    def handle_subscription_updated(db: Session, subscription_data: Dict[str, Any]) -> bool:
        """Handle subscription updates from Stripe webhooks"""
        try:
            stripe_subscription_id = subscription_data["id"]
            
            subscription = db.query(Subscription).filter(
                Subscription.stripe_subscription_id == stripe_subscription_id
            ).first()
            
            if not subscription:
                logger.warning(f"Subscription not found: {stripe_subscription_id}")
                return False
            
            # Update subscription status
            subscription.status = subscription_data["status"]
            subscription.current_period_start = datetime.fromtimestamp(subscription_data["current_period_start"])
            subscription.current_period_end = datetime.fromtimestamp(subscription_data["current_period_end"])
            subscription.cancel_at_period_end = subscription_data["cancel_at_period_end"]
            
            # Reset usage limits at the start of new billing period
            if subscription_data["status"] == "active":
                subscription.monthly_minutes_used = 0
                subscription.phone_calls_used = 0
            
            db.commit()
            
            logger.info(f"Subscription updated: {stripe_subscription_id}")
            return True
            
        except Exception as e:
            logger.error(f"Error handling subscription update: {e}")
            db.rollback()
            return False
    
    @staticmethod
    def cancel_subscription(db: Session, user: User) -> bool:
        """Cancel user's subscription"""
        subscription = db.query(Subscription).filter(Subscription.user_id == user.id).first()
        
        if not subscription or not subscription.stripe_subscription_id:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="No active subscription found"
            )
        
        try:
            # Cancel subscription in Stripe
            stripe.Subscription.modify(
                subscription.stripe_subscription_id,
                cancel_at_period_end=True
            )
            
            # Update local subscription
            subscription.cancel_at_period_end = True
            db.commit()
            
            logger.info(f"Subscription cancelled for user {user.email}")
            return True
            
        except stripe.error.StripeError as e:
            logger.error(f"Error cancelling subscription: {e}")
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail="Failed to cancel subscription"
            )
    
    @staticmethod
    def get_usage_summary(db: Session, user: User) -> Dict[str, Any]:
        """Get user's usage summary for current billing period"""
        subscription = db.query(Subscription).filter(Subscription.user_id == user.id).first()
        
        if not subscription:
            return {
                "plan_type": "free",
                "status": "active",
                "minutes_used": 0,
                "minutes_limit": SUBSCRIPTION_PLANS["free"]["monthly_minutes"],
                "phone_calls_used": 0,
                "phone_calls_limit": SUBSCRIPTION_PLANS["free"]["phone_calls"],
                "current_period_end": None
            }
        
        return {
            "plan_type": subscription.plan_type,
            "status": subscription.status,
            "minutes_used": subscription.monthly_minutes_used,
            "minutes_limit": subscription.monthly_minutes_limit,
            "phone_calls_used": subscription.phone_calls_used,
            "phone_calls_limit": subscription.phone_calls_limit,
            "current_period_end": subscription.current_period_end,
            "cancel_at_period_end": subscription.cancel_at_period_end
        }
