/**
 * Phone Call Functionality for AI Therapist
 */

function initializePhoneMode() {
    loadPhoneData();

    // Set up auto-refresh for call logs and access code
    setInterval(() => {
        if (currentMode === 'phone') {
            loadPhoneData();
        }
    }, 2000); // Refresh every 2 seconds for real-time updates

    // Also refresh immediately when phone mode is activated
    document.addEventListener('modeChanged', (event) => {
        if (event.detail.mode === 'phone') {
            loadPhoneData();
        }
    });

    // Add visual indicator for real-time updates
    const phoneSection = document.querySelector('.phone-section');
    if (phoneSection) {
        const indicator = document.createElement('div');
        indicator.className = 'live-indicator';
        indicator.innerHTML = '<i class="fas fa-circle"></i> Live';
        indicator.style.cssText = `
            position: absolute;
            top: 10px;
            right: 10px;
            background: #10b981;
            color: white;
            padding: 4px 8px;
            border-radius: 12px;
            font-size: 0.8rem;
            font-weight: 600;
        `;
        phoneSection.style.position = 'relative';
        phoneSection.appendChild(indicator);

        // Animate the indicator
        setInterval(() => {
            indicator.style.opacity = '0.5';
            setTimeout(() => {
                indicator.style.opacity = '1';
            }, 500);
        }, 2000);
    }
}

async function loadPhoneData() {
    try {
        const response = await fetch('/phone/config');
        if (response.ok) {
            const data = await response.json();
            displayPhoneInfo(data);
            displayCallLogs(data.call_logs);
        } else {
            throw new Error('Failed to load phone data');
        }
    } catch (error) {
        console.error('Error loading phone data:', error);
        showToast('Failed to load phone information', 'error');
    }
}

function displayPhoneInfo(data) {
    const phoneNumberElement = document.getElementById('phone-number');
    const accessCodeElement = document.getElementById('access-code');
    const totalCallsElement = document.getElementById('total-calls');
    const activeCallsElement = document.getElementById('active-calls');

    if (phoneNumberElement) {
        const newPhoneNumber = data.phone_number;
        if (phoneNumberElement.textContent !== newPhoneNumber) {
            phoneNumberElement.textContent = newPhoneNumber;
            // Flash to indicate update
            phoneNumberElement.style.animation = 'flash 0.5s ease-in-out';
            setTimeout(() => {
                phoneNumberElement.style.animation = '';
            }, 500);
        }
    }

    if (accessCodeElement) {
        const newAccessCode = data.access_code;
        if (accessCodeElement.textContent !== newAccessCode) {
            accessCodeElement.textContent = newAccessCode;
            // Flash to indicate update
            accessCodeElement.style.animation = 'flash 0.5s ease-in-out';
            setTimeout(() => {
                accessCodeElement.style.animation = '';
            }, 500);

            // Show toast notification for access code change
            showToast(`Access code updated: ${newAccessCode}`, 'success');
        }
    }

    if (totalCallsElement) {
        totalCallsElement.textContent = data.total_calls;
    }

    if (activeCallsElement) {
        activeCallsElement.textContent = data.active_calls;
    }
}

function displayCallLogs(callLogs) {
    const callLogsContainer = document.getElementById('call-logs-container');
    if (!callLogsContainer) return;

    if (callLogs.length === 0) {
        callLogsContainer.innerHTML = `
            <div class="no-calls">
                📞 No calls yet. Share your phone number with people who need support!
            </div>
        `;
        return;
    }

    // Sort call logs by timestamp (newest first)
    const sortedLogs = callLogs.sort((a, b) => new Date(b.timestamp) - new Date(a.timestamp));

    callLogsContainer.innerHTML = sortedLogs.map(call => {
        const date = new Date(call.timestamp);
        const timeString = date.toLocaleString();
        const duration = call.duration ? formatDuration(call.duration) : '';
        
        return `
            <div class="call-log-entry">
                <div class="call-info">
                    <div class="call-number">${call.phone_number}</div>
                    <div class="call-time">${timeString} ${duration}</div>
                </div>
                <div class="call-status ${call.status}">${call.status.toUpperCase()}</div>
            </div>
        `;
    }).join('');
}

function formatDuration(seconds) {
    if (!seconds) return '';
    
    const minutes = Math.floor(seconds / 60);
    const remainingSeconds = seconds % 60;
    
    if (minutes > 0) {
        return `(${minutes}m ${remainingSeconds}s)`;
    } else {
        return `(${remainingSeconds}s)`;
    }
}

// Simulate incoming call (for testing)
async function simulateIncomingCall() {
    const testNumber = '******-TEST-' + Math.floor(Math.random() * 1000);
    
    try {
        const response = await fetch('/phone/incoming', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({
                phone_number: testNumber
            })
        });
        
        if (response.ok) {
            showToast(`Incoming call from ${testNumber}`, 'success');
            loadPhoneData(); // Refresh the display
        }
    } catch (error) {
        console.error('Error simulating call:', error);
        showToast('Failed to simulate call', 'error');
    }
}

// Clear all call logs
async function clearCallLogs() {
    if (!confirm('Are you sure you want to clear all call logs?')) {
        return;
    }

    try {
        const response = await fetch('/phone/logs', {
            method: 'DELETE'
        });

        if (response.ok) {
            showToast('Call logs cleared', 'success');
            loadPhoneData(); // Refresh the display
        } else {
            throw new Error('Failed to clear logs');
        }
    } catch (error) {
        console.error('Error clearing call logs:', error);
        showToast('Failed to clear call logs', 'error');
    }
}

// Copy phone information to clipboard
async function copyPhoneInfo() {
    const phoneNumber = document.getElementById('phone-number').textContent;
    const accessCode = document.getElementById('access-code').textContent;

    const phoneInfo = `AI Therapist Phone Support
📞 Phone: ${phoneNumber}
🔢 Access Code: ${accessCode}

Call anytime for 24/7 emotional support!`;

    try {
        await navigator.clipboard.writeText(phoneInfo);
        showToast('📋 Phone info copied to clipboard!', 'success');
    } catch (error) {
        // Fallback for older browsers
        const textArea = document.createElement('textarea');
        textArea.value = phoneInfo;
        document.body.appendChild(textArea);
        textArea.select();
        document.execCommand('copy');
        document.body.removeChild(textArea);
        showToast('📋 Phone info copied to clipboard!', 'success');
    }
}
