"""
Session management utilities for AI Therapist
"""
from typing import Dict, Any
from datetime import datetime

# Global session storage
active_sessions: Dict[str, Dict[str, Any]] = {}

def add_session(session_id: str, session_type: str, handler: Any) -> None:
    """Add a new active session"""
    active_sessions[session_id] = {
        "type": session_type,
        "handler": handler,
        "created_at": datetime.utcnow().isoformat(),
        "user_id": None,
        "user_name": None
    }

def set_session_user(session_id: str, user_id: int, user_name: str) -> None:
    """Set user information for a session"""
    if session_id in active_sessions:
        active_sessions[session_id]["user_id"] = user_id
        active_sessions[session_id]["user_name"] = user_name

def get_session_user(session_id: str) -> tuple:
    """Get user information from session"""
    if session_id in active_sessions:
        session = active_sessions[session_id]
        return session.get("user_id"), session.get("user_name")
    return None, None

def remove_session(session_id: str) -> bool:
    """Remove an active session"""
    if session_id in active_sessions:
        del active_sessions[session_id]
        return True
    return False

def get_session(session_id: str) -> Dict[str, Any]:
    """Get session information"""
    return active_sessions.get(session_id)

def get_all_sessions() -> Dict[str, Dict[str, Any]]:
    """Get all active sessions"""
    return active_sessions.copy()

def cleanup_sessions() -> None:
    """Clean up all active sessions"""
    for session_id, session_info in list(active_sessions.items()):
        handler = session_info.get("handler")
        if handler and hasattr(handler, "shutdown"):
            handler.shutdown()
    active_sessions.clear()


