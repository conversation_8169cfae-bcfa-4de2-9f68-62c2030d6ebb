"""
Audio processing utilities for AI Therapist
"""
import base64
import numpy as np
from io import BytesIO
from PIL import Image

def encode_audio(data: np.ndarray) -> str:
    """Encode Audio data to send to Gemini"""
    return base64.b64encode(data.tobytes()).decode("UTF-8")

def encode_audio_dict(data: np.ndarray) -> dict:
    """Encode Audio data as dict to send to Gemini"""
    return {
        "mime_type": "audio/pcm",
        "data": base64.b64encode(data.tobytes()).decode("UTF-8"),
    }

def encode_image(data: np.ndarray) -> dict:
    """Encode image data to send to Gemini"""
    with BytesIO() as output_bytes:
        pil_image = Image.fromarray(data)
        pil_image.save(output_bytes, "JPEG")
        bytes_data = output_bytes.getvalue()
    base64_str = str(base64.b64encode(bytes_data), "utf-8")
    return {"mime_type": "image/jpeg", "data": base64_str}

def encode_file_content(file_content: bytes, mime_type: str) -> dict:
    """Encode file content to send to Gemini"""
    base64_str = base64.b64encode(file_content).decode("UTF-8")
    return {"mime_type": mime_type, "data": base64_str}

def detect_goodbye(text: str) -> bool:
    """Detect if user is saying goodbye"""
    goodbye_phrases = [
        "bye", "goodbye", "see you later", "talk to you later", 
        "i'm done", "that's all", "gotta go", "have to go",
        "end session", "stop session", "finish session"
    ]
    text_lower = text.lower().strip()
    return any(phrase in text_lower for phrase in goodbye_phrases)
