# Core FastAPI and server dependencies
fastapi==0.104.1
uvicorn[standard]==0.24.0

# Google Gemini AI SDK
google-genai>=0.8.0

# WebRTC and real-time communication - WORKING COMPATIBLE VERSIONS
aiortc==1.4.0
fastrtc==0.0.24

# Audio and Video processing with specific compatible versions
numpy==1.26.4
scipy==1.11.4
av==10.0.0

# Image processing for video support
Pillow>=10.0.0,<11.0.0

# Configuration and environment management
python-dotenv>=1.0.0,<2.0.0
pydantic>=2.4.0,<3.0.0
pydantic-settings>=2.0.0,<3.0.0

# HTTP client and async support
httpx>=0.28.1,<1.0.0
aiofiles>=23.0.0,<24.0.0

# CORS and middleware
python-multipart>=0.0.6

# WebSocket support
websockets>=13.0,<15.0

# Production server optimization
gunicorn>=21.2.0,<22.0.0

# Cloud deployment helpers
psutil>=5.9.0

# Database
sqlalchemy>=2.0.23,<3.0.0
alembic>=1.13.1,<2.0.0

# Authentication and Security
bcrypt>=4.1.2,<5.0.0
pyjwt>=2.8.0,<3.0.0
python-jose[cryptography]>=3.3.0,<4.0.0

# Payment Processing
stripe>=7.8.0,<8.0.0

# Email validation
email-validator>=2.1.0,<3.0.0