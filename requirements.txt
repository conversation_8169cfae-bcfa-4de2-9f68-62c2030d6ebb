# Core FastAPI and web framework
fastapi==0.116.1
uvicorn==0.35.0
starlette==0.47.2

# Google AI and Generative AI
google-generativeai==0.8.5
google-ai-generativelanguage==0.6.15
google-api-core==2.25.1
google-api-python-client==2.177.0
google-auth==2.40.3
google-auth-httplib2==0.2.0
googleapis-common-protos==1.70.0

# Data validation and settings
pydantic==2.11.7
pydantic-core==2.33.2
pydantic-settings==2.10.1

# Database
SQLAlchemy==2.0.42
asyncpg==0.30.0

# HTTP and networking
requests==2.32.4
httplib2==0.22.0
h11==0.16.0
anyio==4.9.0

# Authentication and security
python-jose[cryptography]
passlib[bcrypt]
python-multipart

# Environment and configuration
python-dotenv==1.1.1

# Scientific computing
numpy==1.26.4
scipy==1.11.4

# Utilities
annotated-types==0.7.0
cachetools==5.5.2
certifi==2025.7.14
charset-normalizer==3.4.2
click==8.2.1
colorama==0.4.6
dnspython==2.7.0
email-validator==2.2.0
greenlet==3.2.3
grpcio==1.74.0
grpcio-status==1.71.2
idna==3.10
proto-plus==1.26.1
protobuf==5.29.5
pyasn1==0.6.1
pyasn1-modules==0.4.2
pyparsing==3.2.3
rsa==4.9.1
sniffio==1.3.1
tqdm==4.67.1
typing-inspection==0.4.1
typing-extensions==4.14.1
uritemplate==4.2.0
urllib3==2.5.0

# Payment processing
stripe

# Image processing
Pillow

# WebRTC (optional)
aiortc==1.6.0
fastrtc
