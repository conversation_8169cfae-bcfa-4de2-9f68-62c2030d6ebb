/**
 * AI Therapist Frontend JavaScript
 * Main application logic for voice, video, and text interactions
 */

// Global variables
let peerConnection;
let videoPeerConnection;
let audioContext;
let dataChannel;
let videoDataChannel;
let isRecording = false;
let isVideoRecording = false;
let webrtc_id;
let video_webrtc_id;
let isMuted = false;
let isCameraOn = true;
let analyser_input, dataArray_input;
let analyser, dataArray;
let source_input = null;
let source_output = null;
let currentMode = 'text';
let localStream = null;
let uploadedFiles = [];

// DOM Elements
const startButton = document.getElementById('start-button');
const startVideoButton = document.getElementById('start-video-button');
const toggleCameraBtn = document.getElementById('toggle-camera');
const toggleMicBtn = document.getElementById('toggle-mic');
const voiceSelect = document.getElementById('voice');
const videoVoiceSelect = document.getElementById('video-voice');
const audioOutput = document.getElementById('audio-output');
const localVideo = document.getElementById('local-video');
const localStatus = document.getElementById('local-status');
const boxContainer = document.querySelector('.box-container');
const messagesContainer = document.getElementById('messages');
const userInput = document.getElementById('user-input');
const sendButton = document.getElementById('send-btn');
const micButton = document.getElementById('mic-btn');
const statusIndicator = document.getElementById('status');
const statusText = document.getElementById('status-text');
const uploadZone = document.getElementById('upload-zone');
const fileInput = document.getElementById('file-input');
const uploadedFilesContainer = document.getElementById('uploaded-files');

// Mode switching elements
const modeButtons = document.querySelectorAll('.mode-btn');
const voiceSection = document.querySelector('.voice-section');
const videoSection = document.querySelector('.video-section');
const textSection = document.querySelector('.text-section');

// RTC Configuration
const RTC_CONFIGURATION = {
    iceServers: [
        { urls: ["stun:fr-turn7.xirsys.com"] },
        { urls: ["stun:stun.l.google.com:19302"] },
        { urls: ["stun:stun1.l.google.com:19302"] },
        {
            username: "UIuBt8vNm5tNifOx-1ZY-nlw-mNMjhzc_2LsV1Wjpu2ccJ8-u_6wlgw0j7TxEvi6AAAAAGhSBQhNb29tYXJh",
            credential: "48a4de2c-4bd9-11f0-a9be-6aee953622e2",
            urls: [
                "turn:fr-turn7.xirsys.com:80?transport=udp",
                "turn:fr-turn7.xirsys.com:3478?transport=udp",
                "turn:fr-turn7.xirsys.com:80?transport=tcp",
                "turn:fr-turn7.xirsys.com:3478?transport=tcp",
                "turns:fr-turn7.xirsys.com:443?transport=tcp",
                "turns:fr-turn7.xirsys.com:5349?transport=tcp"
            ]
        }
    ]
};

// SVG Icons for mute toggle
const micIconSVG = `
    <svg xmlns="http://www.w3.org/2000/svg" width="100%" height="100%" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
        <path d="M12 1a3 3 0 0 0-3 3v8a3 3 0 0 0 6 0V4a3 3 0 0 0-3-3z"></path>
        <path d="M19 10v2a7 7 0 0 1-14 0v-2"></path>
        <line x1="12" y1="19" x2="12" y2="23"></line>
        <line x1="8" y1="23" x2="16" y2="23"></line>
    </svg>`;

const micMutedIconSVG = `
    <svg xmlns="http://www.w3.org/2000/svg" width="100%" height="100%" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
        <path d="M12 1a3 3 0 0 0-3 3v8a3 3 0 0 0 6 0V4a3 3 0 0 0-3-3z"></path>
        <path d="M19 10v2a7 7 0 0 1-14 0v-2"></path>
        <line x1="12" y1="19" x2="12" y2="23"></line>
        <line x1="8" y1="23" x2="16" y2="23"></line>
        <line x1="1" y1="1" x2="23" y2="23"></line>
    </svg>`;

// Initialize the application
document.addEventListener('DOMContentLoaded', function() {
    initializeApp();
});

function initializeApp() {
    // Initialize audio visualization
    initializeAudioVisualization();
    
    // Set up mode switching
    setupModeSwitch();
    
    // Set up file upload
    setupFileUpload();
    
    // Set up text chat
    setupTextChat();
    
    // Set up voice controls
    setupVoiceControls();
    
    // Set up video controls
    setupVideoControls();

    // Set default mode
    switchMode('text');
}

function initializeAudioVisualization() {
    const numBars = 32;
    if (boxContainer) {
        for (let i = 0; i < numBars; i++) {
            const box = document.createElement('div');
            box.className = 'box';
            boxContainer.appendChild(box);
        }
    }
}

function setupModeSwitch() {
    modeButtons.forEach(btn => {
        btn.addEventListener('click', () => {
            const mode = btn.dataset.mode;
            switchMode(mode);
        });
    });
}

function switchMode(mode) {
    currentMode = mode;

    modeButtons.forEach(btn => {
        btn.classList.toggle('active', btn.dataset.mode === mode);
    });

    // Hide all sections
    if (voiceSection) voiceSection.classList.remove('active');
    if (videoSection) videoSection.classList.remove('active');
    if (textSection) textSection.classList.remove('active');

    // Show selected section
    if (mode === 'voice' && voiceSection) {
        voiceSection.classList.add('active');
    } else if (mode === 'video' && videoSection) {
        videoSection.classList.add('active');
        initializeVideoMode();
    } else if (textSection) {
        textSection.classList.add('active');
        initializeTextMode();
    }
}

// Utility functions
function showToast(message, type = 'error') {
    const toast = document.getElementById('error-toast');
    if (toast) {
        toast.textContent = message;
        toast.className = `toast ${type}`;
        toast.style.display = 'block';

        setTimeout(() => {
            toast.style.display = 'none';
        }, 5000);
    }
}

function showStatus(message) {
    if (statusText && statusIndicator) {
        statusText.textContent = message;
        statusIndicator.classList.add('visible');
        
        setTimeout(() => {
            statusIndicator.classList.remove('visible');
        }, 3000);
    }
}
