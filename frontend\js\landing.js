// Landing page functionality
document.addEventListener('DOMContentLoaded', function() {
    // Check if user is already logged in
    const authToken = localStorage.getItem('auth_token');
    if (authToken) {
        // Update navigation for logged-in user
        updateNavForLoggedInUser();
    }

    // Mobile menu toggle
    const hamburger = document.querySelector('.hamburger');
    const navMenu = document.querySelector('.nav-menu');

    if (hamburger && navMenu) {
        hamburger.addEventListener('click', function() {
            navMenu.classList.toggle('active');
            hamburger.classList.toggle('active');
        });
    }
    
    // Smooth scrolling for anchor links
    document.querySelectorAll('a[href^="#"]').forEach(anchor => {
        anchor.addEventListener('click', function (e) {
            e.preventDefault();
            const target = document.querySelector(this.getAttribute('href'));
            if (target) {
                target.scrollIntoView({
                    behavior: 'smooth',
                    block: 'start'
                });
            }
        });
    });
    
    // Navbar scroll effect - Dark theme
    window.addEventListener('scroll', function() {
        const navbar = document.querySelector('.navbar');
        if (window.scrollY > 50) {
            navbar.style.background = 'rgba(15, 23, 42, 0.98)';
            navbar.style.boxShadow = '0 2px 20px rgba(0, 0, 0, 0.3)';
        } else {
            navbar.style.background = 'rgba(15, 23, 42, 0.95)';
            navbar.style.boxShadow = 'none';
        }
    });
    
    // Load pricing plans
    loadPricingPlans();
    
    // Intersection Observer for animations
    const observerOptions = {
        threshold: 0.1,
        rootMargin: '0px 0px -50px 0px'
    };
    
    const observer = new IntersectionObserver(function(entries) {
        entries.forEach(entry => {
            if (entry.isIntersecting) {
                entry.target.style.opacity = '1';
                entry.target.style.transform = 'translateY(0)';
            }
        });
    }, observerOptions);
    
    // Observe elements for animation
    document.querySelectorAll('.feature-card, .benefit-item, .pricing-card').forEach(el => {
        el.style.opacity = '0';
        el.style.transform = 'translateY(30px)';
        el.style.transition = 'opacity 0.6s ease, transform 0.6s ease';
        observer.observe(el);
    });
});

// Load pricing plans from API
async function loadPricingPlans() {
    try {
        const response = await fetch('/payments/plans');
        const data = await response.json();
        
        if (data.plans) {
            renderPricingPlans(data.plans);
        }
    } catch (error) {
        console.error('Error loading pricing plans:', error);
        // Fallback to static plans
        renderFallbackPlans();
    }
}

// Render pricing plans
function renderPricingPlans(plans) {
    const container = document.getElementById('pricing-plans');
    if (!container) return;
    
    container.innerHTML = '';
    
    // Define plan order and which one is popular
    const planOrder = ['free', 'basic', 'premium', 'enterprise'];
    const popularPlan = 'premium';
    
    planOrder.forEach(planType => {
        const plan = plans[planType];
        if (plan) {
            const planCard = createPricingCard(planType, plan, planType === popularPlan);
            container.appendChild(planCard);
        }
    });
}

// Create pricing card element
function createPricingCard(planType, plan, isPopular = false) {
    const card = document.createElement('div');
    card.className = `pricing-card ${isPopular ? 'popular' : ''}`;
    
    const price = planType === 'free' ? 0 : (plan.price_cents / 100);
    const isFreePlan = planType === 'free';
    
    card.innerHTML = `
        <div class="plan-name">${plan.name}</div>
        <div class="plan-price">
            <span class="currency">$</span>${price.toFixed(0)}
            <span class="period">${isFreePlan ? '' : '/month'}</span>
        </div>
        <div class="plan-description">
            ${isFreePlan ? 'Perfect for trying out our AI therapist' : 
              planType === 'basic' ? 'Great for regular therapy sessions' :
              planType === 'premium' ? 'Unlimited access to all features' :
              'Enterprise-grade therapy solution'}
        </div>
        <ul class="plan-features">
            ${plan.features.map(feature => `<li>${feature}</li>`).join('')}
        </ul>
        <button class="plan-button ${isPopular ? 'primary' : 'secondary'}" 
                onclick="handlePlanSelection('${planType}')">
            ${isFreePlan ? 'Get Started Free' : `Choose ${plan.name}`}
        </button>
    `;
    
    return card;
}

// Handle plan selection
function handlePlanSelection(planType) {
    const authToken = localStorage.getItem('auth_token');

    if (authToken) {
        // User is logged in, redirect to app or payment
        if (planType === 'free') {
            window.location.href = '/app';
        } else {
            // Redirect to payment flow (this would be handled in the app)
            window.location.href = `/app?upgrade=${planType}`;
        }
    } else {
        // User not logged in, redirect to signup
        if (planType === 'free') {
            window.location.href = '/signup';
        } else {
            window.location.href = `/signup?plan=${planType}`;
        }
    }
}

// Fallback plans if API fails
function renderFallbackPlans() {
    const fallbackPlans = {
        free: {
            name: 'Free Plan',
            price_cents: 0,
            features: [
                'Text chat support',
                '30 minutes voice/video per month',
                'Basic AI responses',
                'Community support'
            ]
        },
        basic: {
            name: 'Basic Plan',
            price_cents: 1999,
            features: [
                'Everything in Free',
                '5 hours voice/video per month',
                '10 phone calls per month',
                'Priority AI responses',
                'Email support'
            ]
        },
        premium: {
            name: 'Premium Plan',
            price_cents: 4999,
            features: [
                'Everything in Basic',
                'Unlimited voice/video',
                'Unlimited phone calls',
                'Advanced AI personality',
                '24/7 priority support',
                'Session analytics'
            ]
        },
        enterprise: {
            name: 'Enterprise Plan',
            price_cents: 9999,
            features: [
                'Everything in Premium',
                'Custom AI training',
                'Team management',
                'Advanced security',
                'Dedicated support',
                'Custom integrations'
            ]
        }
    };
    
    renderPricingPlans(fallbackPlans);
}

// Toast notification system
function showToast(message, type = 'info') {
    const toast = document.createElement('div');
    toast.className = `toast toast-${type}`;
    toast.textContent = message;
    
    // Add toast styles
    toast.style.cssText = `
        position: fixed;
        top: 20px;
        right: 20px;
        background: ${type === 'success' ? '#10b981' : type === 'error' ? '#ef4444' : '#6366f1'};
        color: white;
        padding: 1rem 1.5rem;
        border-radius: 0.5rem;
        box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1);
        z-index: 10000;
        transform: translateX(100%);
        transition: transform 0.3s ease;
    `;
    
    document.body.appendChild(toast);
    
    // Animate in
    setTimeout(() => {
        toast.style.transform = 'translateX(0)';
    }, 100);
    
    // Remove after 3 seconds
    setTimeout(() => {
        toast.style.transform = 'translateX(100%)';
        setTimeout(() => {
            document.body.removeChild(toast);
        }, 300);
    }, 3000);
}

// Add mobile menu styles
const mobileMenuStyles = `
    @media (max-width: 768px) {
        .nav-menu {
            position: fixed;
            left: -100%;
            top: 70px;
            flex-direction: column;
            background-color: white;
            width: 100%;
            text-align: center;
            transition: 0.3s;
            box-shadow: 0 10px 27px rgba(0, 0, 0, 0.05);
            padding: 2rem 0;
        }
        
        .nav-menu.active {
            left: 0;
        }
        
        .nav-menu a {
            display: block;
            margin: 1rem 0;
        }
        
        .hamburger.active span:nth-child(1) {
            transform: rotate(-45deg) translate(-5px, 6px);
        }
        
        .hamburger.active span:nth-child(2) {
            opacity: 0;
        }
        
        .hamburger.active span:nth-child(3) {
            transform: rotate(45deg) translate(-5px, -6px);
        }
    }
`;

// Add mobile styles to head
const styleSheet = document.createElement('style');
styleSheet.textContent = mobileMenuStyles;
document.head.appendChild(styleSheet);

// Update navigation for logged-in users
function updateNavForLoggedInUser() {
    const navMenu = document.querySelector('.nav-menu');
    if (navMenu) {
        // Replace login/signup buttons with app and logout buttons
        const loginBtn = navMenu.querySelector('.login-btn');
        const signupBtn = navMenu.querySelector('.signup-btn');

        if (loginBtn) {
            loginBtn.textContent = 'Dashboard';
            loginBtn.href = '/app';
            loginBtn.className = 'nav-btn signup-btn'; // Use signup button style
        }

        if (signupBtn) {
            signupBtn.textContent = 'Logout';
            signupBtn.href = '#';
            signupBtn.className = 'nav-btn login-btn'; // Use login button style
            signupBtn.onclick = function(e) {
                e.preventDefault();
                logout();
            };
        }
    }
}

// Logout function
function logout() {
    localStorage.removeItem('auth_token');
    localStorage.removeItem('refresh_token');
    showToast('Logged out successfully', 'info');

    // Reload page to reset navigation
    setTimeout(() => {
        window.location.reload();
    }, 1000);
}
