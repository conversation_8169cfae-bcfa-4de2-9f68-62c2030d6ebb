// Authentication pages functionality

// Initialize login page
function initializeLoginPage() {
    const loginForm = document.getElementById('login-form');
    if (loginForm) {
        loginForm.addEventListener('submit', handleLogin);
    }
    
    // Check if user is already logged in
    const token = localStorage.getItem('auth_token');
    if (token) {
        // Redirect to app
        window.location.href = '/app';
    }
}

// Initialize signup page
function initializeSignupPage() {
    const signupForm = document.getElementById('signup-form');
    if (signupForm) {
        signupForm.addEventListener('submit', handleSignup);
    }
    
    // Password strength checker
    const passwordInput = document.getElementById('password');
    if (passwordInput) {
        passwordInput.addEventListener('input', checkPasswordStrength);
    }
    
    // Check for plan parameter
    const urlParams = new URLSearchParams(window.location.search);
    const selectedPlan = urlParams.get('plan');
    if (selectedPlan) {
        showPlanPreview(selectedPlan);
    }
    
    // Check if user is already logged in
    const token = localStorage.getItem('auth_token');
    if (token) {
        // Redirect to app
        window.location.href = '/app';
    }
}

// Handle login form submission
async function handleLogin(event) {
    event.preventDefault();
    
    const email = document.getElementById('email').value;
    const password = document.getElementById('password').value;
    const rememberMe = document.getElementById('remember-me').checked;
    
    const loginBtn = document.getElementById('login-btn');
    const btnText = loginBtn.querySelector('.btn-text');
    const btnLoader = loginBtn.querySelector('.btn-loader');
    const errorMessage = document.getElementById('error-message');
    
    // Show loading state
    loginBtn.disabled = true;
    btnText.style.display = 'none';
    btnLoader.style.display = 'block';
    errorMessage.style.display = 'none';
    
    try {
        const response = await fetch('/auth/login', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({ email, password })
        });
        
        const data = await response.json();
        
        if (response.ok) {
            // Store tokens
            localStorage.setItem('auth_token', data.access_token);
            localStorage.setItem('refresh_token', data.refresh_token);

            if (rememberMe) {
                localStorage.setItem('remember_me', 'true');
            }

            showToast('Login successful! Redirecting...', 'success');

            // Redirect to app immediately
            window.location.href = '/app';
            
        } else {
            errorMessage.textContent = data.detail || 'Login failed. Please try again.';
            errorMessage.style.display = 'block';
        }
    } catch (error) {
        console.error('Login error:', error);
        errorMessage.textContent = 'Network error. Please check your connection and try again.';
        errorMessage.style.display = 'block';
    } finally {
        // Reset button state
        loginBtn.disabled = false;
        btnText.style.display = 'block';
        btnLoader.style.display = 'none';
    }
}

// Handle signup form submission
async function handleSignup(event) {
    event.preventDefault();
    
    const firstName = document.getElementById('first-name').value;
    const lastName = document.getElementById('last-name').value;
    const email = document.getElementById('email').value;
    const username = document.getElementById('username').value;
    const password = document.getElementById('password').value;
    const confirmPassword = document.getElementById('confirm-password').value;
    const terms = document.getElementById('terms').checked;
    const newsletter = document.getElementById('newsletter').checked;
    
    const signupBtn = document.getElementById('signup-btn');
    const btnText = signupBtn.querySelector('.btn-text');
    const btnLoader = signupBtn.querySelector('.btn-loader');
    const errorMessage = document.getElementById('error-message');
    
    // Validation
    if (password !== confirmPassword) {
        errorMessage.textContent = 'Passwords do not match.';
        errorMessage.style.display = 'block';
        return;
    }
    
    if (!terms) {
        errorMessage.textContent = 'Please accept the Terms of Service and Privacy Policy.';
        errorMessage.style.display = 'block';
        return;
    }
    
    // Show loading state
    signupBtn.disabled = true;
    btnText.style.display = 'none';
    btnLoader.style.display = 'block';
    errorMessage.style.display = 'none';
    
    try {
        const response = await fetch('/auth/register', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({
                email,
                username,
                password,
                full_name: `${firstName} ${lastName}`.trim()
            })
        });
        
        const data = await response.json();
        
        if (response.ok) {
            showToast('Account created successfully! Please login.', 'success');
            
            // Redirect to login with email pre-filled
            setTimeout(() => {
                window.location.href = `/login?email=${encodeURIComponent(email)}`;
            }, 1500);
            
        } else {
            errorMessage.textContent = data.detail || 'Registration failed. Please try again.';
            errorMessage.style.display = 'block';
        }
    } catch (error) {
        console.error('Signup error:', error);
        errorMessage.textContent = 'Network error. Please check your connection and try again.';
        errorMessage.style.display = 'block';
    } finally {
        // Reset button state
        signupBtn.disabled = false;
        btnText.style.display = 'block';
        btnLoader.style.display = 'none';
    }
}

// Toggle password visibility
function togglePassword() {
    const passwordInput = document.getElementById('password');
    const toggleIcon = document.getElementById('password-toggle-icon');
    
    if (passwordInput.type === 'password') {
        passwordInput.type = 'text';
        toggleIcon.className = 'fas fa-eye-slash';
    } else {
        passwordInput.type = 'password';
        toggleIcon.className = 'fas fa-eye';
    }
}

// Check password strength
function checkPasswordStrength(event) {
    const password = event.target.value;
    const strengthFill = document.getElementById('strength-fill');
    const strengthText = document.getElementById('strength-text');
    
    if (!strengthFill || !strengthText) return;
    
    let score = 0;
    let feedback = '';
    
    // Length check
    if (password.length >= 8) score += 1;
    if (password.length >= 12) score += 1;
    
    // Character variety checks
    if (/[a-z]/.test(password)) score += 1;
    if (/[A-Z]/.test(password)) score += 1;
    if (/[0-9]/.test(password)) score += 1;
    if (/[^A-Za-z0-9]/.test(password)) score += 1;
    
    // Set strength level
    if (password.length === 0) {
        strengthFill.style.width = '0%';
        strengthFill.style.background = '#e5e7eb';
        feedback = 'Password strength';
    } else if (score < 3) {
        strengthFill.style.width = '25%';
        strengthFill.style.background = '#ef4444';
        feedback = 'Weak password';
    } else if (score < 5) {
        strengthFill.style.width = '50%';
        strengthFill.style.background = '#f59e0b';
        feedback = 'Fair password';
    } else if (score < 6) {
        strengthFill.style.width = '75%';
        strengthFill.style.background = '#10b981';
        feedback = 'Good password';
    } else {
        strengthFill.style.width = '100%';
        strengthFill.style.background = '#059669';
        feedback = 'Strong password';
    }
    
    strengthText.textContent = feedback;
}

// Show plan preview in signup
async function showPlanPreview(planType) {
    try {
        const response = await fetch('/payments/plans');
        const data = await response.json();
        
        if (data.plans && data.plans[planType]) {
            const plan = data.plans[planType];
            const preview = document.getElementById('plan-preview');
            const planName = document.getElementById('selected-plan-name');
            const planPrice = document.getElementById('selected-plan-price');
            const planFeatures = document.getElementById('selected-plan-features');
            
            if (preview && planName && planPrice && planFeatures) {
                planName.textContent = plan.name;
                planPrice.textContent = planType === 'free' ? 'Free' : `$${(plan.price_cents / 100).toFixed(0)}/month`;
                planFeatures.innerHTML = plan.features.slice(0, 3).map(feature => `• ${feature}`).join('<br>');
                
                preview.style.display = 'block';
            }
        }
    } catch (error) {
        console.error('Error loading plan preview:', error);
    }
}

// Social login functions
function loginWithGoogle() {
    showToast('Google login coming soon!', 'info');
}

function signupWithGoogle() {
    showToast('Google signup coming soon!', 'info');
}

function loginWithApple() {
    showToast('Apple login coming soon!', 'info');
}

function signupWithApple() {
    showToast('Apple signup coming soon!', 'info');
}

// Toast notification system
function showToast(message, type = 'info') {
    const container = document.getElementById('toast-container') || document.body;
    
    const toast = document.createElement('div');
    toast.className = `toast toast-${type}`;
    toast.textContent = message;
    
    container.appendChild(toast);
    
    // Animate in
    setTimeout(() => {
        toast.classList.add('show');
    }, 100);
    
    // Remove after 3 seconds
    setTimeout(() => {
        toast.classList.remove('show');
        setTimeout(() => {
            if (container.contains(toast)) {
                container.removeChild(toast);
            }
        }, 300);
    }, 3000);
}

// Pre-fill email from URL parameter (for login page)
document.addEventListener('DOMContentLoaded', function() {
    const urlParams = new URLSearchParams(window.location.search);
    const email = urlParams.get('email');
    const emailInput = document.getElementById('email');
    
    if (email && emailInput) {
        emailInput.value = email;
        // Focus on password field if email is pre-filled
        const passwordInput = document.getElementById('password');
        if (passwordInput) {
            passwordInput.focus();
        }
    }
});
