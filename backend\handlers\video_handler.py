"""
Gemini Video WebRTC handler for AI Therapist
"""
import asyncio
import time
import logging
import numpy as np
from google import genai

from backend.config.settings import get_settings, SYSTEM_PROMPT
from backend.services.webrtc_service import AsyncAudioVideoStream<PERSON>andler, FASTRTC_AVAILABLE, wait_for_item
from backend.utils.audio_utils import encode_audio_dict, encode_image, detect_goodbye
from backend.utils.session_manager import add_session, remove_session, get_session_user
from backend.services.memory_service import memory_service
from backend.models.database import get_db

logger = logging.getLogger(__name__)

class GeminiVideoHandler(AsyncAudioVideoStreamHandler):
    """Enhanced handler for Gemini API with audio and video capabilities for therapeutic sessions"""

    def __init__(self) -> None:
        super().__init__(
            expected_layout="mono",
            output_sample_rate=24000,
            input_sample_rate=16000,
        )
        self.audio_queue = asyncio.Queue()
        self.session = None
        self.last_frame_time = 0
        self.quit = asyncio.Event()
        self.session_id = None
        self.uploaded_files = []  # Store uploaded files
        self.settings = get_settings()
        self.phone_mode = self.settings.mode == "PHONE"

    def copy(self) -> "GeminiVideoHandler":
        return GeminiVideoHandler()

    async def start_up(self):
        if not FASTRTC_AVAILABLE:
            logger.warning("FastRTC not available, skipping video handler setup")
            return

        try:
            # Get voice name from args
            voice_name = "Aoede"
            if not self.phone_mode:
                try:
                    await asyncio.wait_for(self.wait_for_args(), timeout=5.0)
                    if self.latest_args and len(self.latest_args) > 1:
                        voice_name = self.latest_args[1]
                        # Check if there are uploaded files in args
                        if len(self.latest_args) > 2 and self.latest_args[2]:
                            self.uploaded_files = self.latest_args[2]
                except (asyncio.TimeoutError, Exception) as args_error:
                    logger.warning(f"Could not get args, using default voice: {args_error}")

            logger.info(f"Starting video handler with voice: {voice_name}")

            client = genai.Client(
                api_key=self.settings.gemini_api_key,
                http_options={"api_version": "v1alpha"}
            )
            
            config = {
                "response_modalities": ["AUDIO"],
                "speech_config": {
                    "voice_config": {
                        "prebuilt_voice_config": {
                            "voice_name": voice_name
                        }
                    }
                },
                "generation_config": {
                    "temperature": 0.8,
                    "max_output_tokens": 256,
                },
                "system_instruction": {
                    "parts": [{"text": SYSTEM_PROMPT}]
                }
            }
            
            async with client.aio.live.connect(
                model="gemini-2.0-flash-exp",
                config=config,
            ) as session:
                self.session = session
                self.session_id = f"video_{int(time.time())}"
                add_session(self.session_id, "video", self)
                logger.info(f"Gemini Video Live session established with voice: {voice_name}")
                
                # Send identity reinforcement
                try:
                    identity_msg = (
                        f"IMPORTANT: You are an AI Therapist created by Critical Future. "
                        f"Your voice name is {voice_name}. You can see the person you're helping. "
                        f"Use their visual cues to provide better therapeutic support. "
                        f"Never say you are created by Google or Gemini. Always say Critical Future."
                    )
                    await session.send(input={"text": identity_msg})

                    # Add user memory context if available
                    user_id, user_name = get_session_user(self.session_id)
                    if user_id:
                        try:
                            db = next(get_db())
                            user_context = memory_service.get_user_context(user_id, db)
                            if user_context:
                                context_msg = f"USER MEMORY CONTEXT:\n{user_context}"
                                await session.send(input={"text": context_msg})
                                logger.info(f"User memory context sent for user {user_id}")
                            db.close()
                        except Exception as memory_error:
                            logger.warning(f"Could not load user memory: {memory_error}")

                    # Send uploaded files if any
                    if self.uploaded_files:
                        for file_data in self.uploaded_files:
                            await session.send(input=file_data)
                            logger.info("Uploaded file sent to AI")
                    
                    logger.info("Video session identity reinforcement sent")
                except Exception as prompt_error:
                    logger.warning(f"Could not send video session setup: {prompt_error}")
                
                while not self.quit.is_set():
                    turn = self.session.receive()
                    try:
                        async for response in turn:
                            if data := response.data:
                                audio = np.frombuffer(data, dtype=np.int16).reshape(1, -1)
                                self.audio_queue.put_nowait(audio)
                                
                            # Check for goodbye in text responses
                            if hasattr(response, 'text') and response.text:
                                if detect_goodbye(response.text):
                                    logger.info("Goodbye detected in video session, ending")
                                    await asyncio.sleep(2)  # Let the goodbye message play
                                    self.quit.set()
                                    break
                                    
                    except Exception as e:
                        logger.error(f"Video session error: {e}")
                        break
                        
        except Exception as e:
            logger.error(f"Error in GeminiVideoHandler start_up: {e}")
        finally:
            # Clean up session
            if self.session_id:
                remove_session(self.session_id)

    async def video_receive(self, frame: np.ndarray):
        """Receive video frame from client"""
        if self.session and frame is not None and frame.size > 0:
            # Send image every 2 seconds to avoid overwhelming the API
            if time.time() - self.last_frame_time > 2:
                self.last_frame_time = time.time()
                try:
                    await self.session.send(input=encode_image(frame))
                except Exception as e:
                    logger.warning(f"Error sending video frame: {e}")

    async def video_emit(self):
        """Video emit - AI doesn't send video back, only audio"""
        # Return None since AI doesn't send video back to user
        # FastRTC requires this method for send-receive mode
        return None

    async def receive(self, frame: tuple[int, np.ndarray]) -> None:
        """Receive audio from client"""
        try:
            if frame is None or len(frame) != 2:
                return

            _, array = frame
            if array is None:
                return

            array = array.squeeze()
            if array is None or array.size == 0:
                return

            audio_message = encode_audio_dict(array)
            if self.session and audio_message:
                await self.session.send(input=audio_message)
        except Exception as e:
            logger.error(f"Error processing audio in video session: {e}")

    async def emit(self):
        """Emit audio response to client"""
        try:
            array = await wait_for_item(self.audio_queue, 0.01)
            if array is not None:
                return (self.output_sample_rate, array)
            return None
        except Exception as e:
            logger.error(f"Error emitting audio in video session: {e}")
            return None

    async def shutdown(self) -> None:
        """Clean shutdown of video handler"""
        logger.info("Shutting down Gemini video handler")
        if self.session:
            self.quit.set()
            try:
                await self.session.close()
            except Exception as e:
                logger.warning(f"Error closing video session: {e}")
            self.quit.clear()
            
        # Clean up session
        if self.session_id:
            remove_session(self.session_id)
