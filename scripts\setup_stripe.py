#!/usr/bin/env python3
"""
Script to set up Stripe products and prices for AI Therapist
"""
import os
import stripe
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

# Configure Stripe
stripe.api_key = os.getenv("STRIPE_SECRET_KEY")

def create_stripe_products():
    """Create Stripe products and prices"""
    
    print("🔧 Setting up Stripe products and prices...")
    
    # Product definitions
    products = [
        {
            "name": "AI Therapist Basic Plan",
            "description": "5 hours voice/video per month + 10 phone calls",
            "price_cents": 1999,  # $19.99
            "price_id_key": "basic"
        },
        {
            "name": "AI Therapist Premium Plan", 
            "description": "Unlimited voice/video and phone calls with priority support",
            "price_cents": 4999,  # $49.99
            "price_id_key": "premium"
        },
        {
            "name": "AI Therapist Enterprise Plan",
            "description": "Everything in Premium plus custom integrations and dedicated support",
            "price_cents": 9999,  # $99.99
            "price_id_key": "enterprise"
        }
    ]
    
    created_prices = {}
    
    for product_info in products:
        try:
            # Create product
            print(f"Creating product: {product_info['name']}")
            product = stripe.Product.create(
                name=product_info['name'],
                description=product_info['description'],
                metadata={
                    'plan_type': product_info['price_id_key']
                }
            )
            
            # Create price
            print(f"Creating price for {product_info['name']}: ${product_info['price_cents']/100:.2f}/month")
            price = stripe.Price.create(
                product=product.id,
                unit_amount=product_info['price_cents'],
                currency='usd',
                recurring={'interval': 'month'},
                metadata={
                    'plan_type': product_info['price_id_key']
                }
            )
            
            created_prices[product_info['price_id_key']] = price.id
            print(f"✅ Created price ID: {price.id}")
            
        except stripe.error.StripeError as e:
            print(f"❌ Error creating {product_info['name']}: {e}")
            continue
    
    # Print summary
    print("\n📋 Summary of created prices:")
    for plan_type, price_id in created_prices.items():
        print(f"{plan_type}: {price_id}")
    
    # Generate code to update database.py
    print("\n🔧 Update your database.py with these price IDs:")
    print("```python")
    for plan_type, price_id in created_prices.items():
        print(f'        "stripe_price_id": "{price_id}"  # {plan_type.title()} plan')
    print("```")
    
    return created_prices

def list_existing_products():
    """List existing Stripe products"""
    print("📋 Existing Stripe products:")
    
    try:
        products = stripe.Product.list(limit=10)
        for product in products.data:
            print(f"Product: {product.name} (ID: {product.id})")
            
            # Get prices for this product
            prices = stripe.Price.list(product=product.id)
            for price in prices.data:
                amount = price.unit_amount / 100 if price.unit_amount else 0
                interval = price.recurring.interval if price.recurring else 'one-time'
                print(f"  Price: ${amount:.2f}/{interval} (ID: {price.id})")
    
    except stripe.error.StripeError as e:
        print(f"❌ Error listing products: {e}")

def main():
    """Main function"""
    if not stripe.api_key:
        print("❌ STRIPE_SECRET_KEY not found in environment variables")
        print("Please set your Stripe secret key in the .env file")
        return
    
    print("🚀 AI Therapist Stripe Setup")
    print("=" * 40)
    
    # List existing products first
    list_existing_products()
    print()
    
    # Ask user if they want to create new products
    response = input("Do you want to create new products? (y/N): ").lower().strip()
    
    if response in ['y', 'yes']:
        created_prices = create_stripe_products()
        
        if created_prices:
            print("\n✅ Stripe setup completed successfully!")
            print("Don't forget to update your webhook endpoint in Stripe dashboard:")
            print("https://dashboard.stripe.com/webhooks")
            print("Endpoint URL: https://your-domain.com/payments/webhook")
            print("Events to listen for:")
            print("- checkout.session.completed")
            print("- customer.subscription.updated")
            print("- customer.subscription.deleted")
            print("- invoice.payment_succeeded")
            print("- invoice.payment_failed")
        else:
            print("❌ No products were created")
    else:
        print("Skipping product creation")

if __name__ == "__main__":
    main()
