"""
WebRTC service configuration for AI Therapist
"""
import logging
from typing import Dict, Any

logger = logging.getLogger(__name__)

# WebRTC availability flag
FASTRTC_AVAILABLE = False
FASTRTC_ERROR = None

# Try to import FastRTC components
try:
    from aiortc import AudioStreamTrack, VideoStreamTrack, RTCPeerConnection
    logger.info("✅ aiortc components available")
    
    from fastrtc import (
        AsyncStreamHandler,
        AsyncAudioVideoStreamHandler,
        Stream,
        get_cloudflare_turn_credentials_async,
        wait_for_item,
    )
    
    from fastrtc.tracks import EmitType, StreamHandler
    FASTRTC_AVAILABLE = True
    logger.info("✅ FastRTC imported successfully with all components")
    
except ImportError as e:
    FASTRTC_ERROR = str(e)
    logger.error(f"❌ FastRTC/aiortc compatibility issue: {e}")
    
    if "AudioStreamTrack" in str(e):
        logger.error("🔧 This is a known aiortc version compatibility issue")
        logger.error("💡 Try: aiortc==1.6.0 or use text-only mode")
    
    # Create fallback classes
    class AsyncStreamHandler:
        def __init__(self, expected_layout="mono", output_sample_rate=24000, input_sample_rate=16000):
            self.expected_layout = expected_layout
            self.output_sample_rate = output_sample_rate
            self.input_sample_rate = input_sample_rate
            self.phone_mode = False
            self.latest_args = None
            logger.warning("Using fallback AsyncStreamHandler")
        
        async def wait_for_args(self):
            pass
        
        async def start_up(self):
            logger.info("Fallback handler startup - voice features disabled")
        
        async def receive(self, frame):
            pass
        
        async def emit(self):
            return None
        
        def shutdown(self):
            pass
        
        def copy(self):
            return AsyncStreamHandler(self.expected_layout, self.output_sample_rate)
    
    class AsyncAudioVideoStreamHandler(AsyncStreamHandler):
        def __init__(self, expected_layout="mono", output_sample_rate=24000, input_sample_rate=16000):
            super().__init__(expected_layout, output_sample_rate, input_sample_rate)
            logger.warning("Using fallback AsyncAudioVideoStreamHandler")
        
        async def video_receive(self, frame):
            pass
        
        async def video_emit(self):
            return None
        
        def copy(self):
            return AsyncAudioVideoStreamHandler(self.expected_layout, self.output_sample_rate)
    
    class Stream:
        def __init__(self, modality="audio", mode="send-receive", handler=None, **kwargs):
            self.modality = modality
            self.mode = mode 
            self.handler = handler or AsyncStreamHandler()
            logger.warning("Using fallback Stream implementation")
            logger.info("Voice and video features are disabled - text chat fully functional")
        
        def mount(self, app, path=""):
            # Add fallback WebRTC endpoints
            endpoint_path = f"{path}/webrtc/offer" if path else "/webrtc/offer"
            status_path = f"{path}/webrtc/status" if path else "/webrtc/status"
            
            @app.post(endpoint_path)
            async def webrtc_offer_fallback(request):
                body = await request.json()
                return {
                    "status": "failed",
                    "meta": {
                        "error": "webrtc_not_available",
                        "message": f"WebRTC features are not available: {FASTRTC_ERROR}",
                        "suggestion": "Use text chat mode instead"
                    }
                }
            
            @app.get(status_path)
            async def webrtc_status():
                return {
                    "available": False,
                    "error": FASTRTC_ERROR,
                    "text_chat_available": True
                }
        
        def set_input(self, webrtc_id, voice_name, uploaded_files=None):
            logger.warning(f"Stream.set_input() called but WebRTC not available: {FASTRTC_ERROR}")
    
    async def get_cloudflare_turn_credentials_async():
        return {
            "iceServers": [
                { "urls": ["stun:fr-turn7.xirsys.com"] },
                { "urls": ["stun:stun.l.google.com:19302"] },
                { "urls": ["stun:stun1.l.google.com:19302"] },
                {
                    "username": "UIuBt8vNm5tNifOx-1ZY-nlw-mNMjhzc_2LsV1Wjpu2ccJ8-u_6wlgw0j7TxEvi6AAAAAGhSBQhNb29tYXJh",
                    "credential": "48a4de2c-4bd9-11f0-a9be-6aee953622e2",
                    "urls": [
                        "turn:fr-turn7.xirsys.com:80?transport=udp",
                        "turn:fr-turn7.xirsys.com:3478?transport=udp",
                        "turn:fr-turn7.xirsys.com:80?transport=tcp",
                        "turn:fr-turn7.xirsys.com:3478?transport=tcp",
                        "turns:fr-turn7.xirsys.com:443?transport=tcp",
                        "turns:fr-turn7.xirsys.com:5349?transport=tcp"
                    ]
                }
            ]
        }

    async def wait_for_item(queue, timeout):
        return None

except Exception as e:
    FASTRTC_ERROR = f"Unexpected error: {str(e)}"
    logger.error(f"❌ Unexpected FastRTC error: {e}")
    FASTRTC_AVAILABLE = False

def is_webrtc_available() -> bool:
    """Check if WebRTC is available"""
    return FASTRTC_AVAILABLE

def get_webrtc_error() -> str:
    """Get WebRTC error message"""
    return FASTRTC_ERROR or "No error"
