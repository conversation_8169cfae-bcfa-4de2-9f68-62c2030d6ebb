"""
Chat routes for AI Therapist
"""
import logging
from fastapi import APIRouter, WebSocket, WebSocketDisconnect, HTTPException, Depends
from sqlalchemy.orm import Session
from backend.models.schemas import ChatRequest, ChatResponse
from backend.config.settings import SYSTEM_PROMPT
from backend.services.gemini_service import GeminiService
from backend.services.memory_service import memory_service
from backend.utils.audio_utils import detect_goodbye
from backend.models.database import get_db, User
from backend.routes.auth import get_authenticated_user

logger = logging.getLogger(__name__)
router = APIRouter()

@router.post("/chat", response_model=ChatResponse)
async def chat_endpoint(
    request: ChatRequest,
    user: User = Depends(get_authenticated_user),
    db: Session = Depends(get_db)
):
    """Text-based chat endpoint using Gemini with memory"""
    try:
        # Check for goodbye in text chat
        if detect_goodbye(request.prompt):
            return ChatResponse(response="Thank you for sharing with me today. Take care of yourself, and remember that you have the strength to handle whatever comes your way. Feel free to come back anytime you need support. Goodbye for now! 💙")

        # Process user message and get memory context
        session_id = f"text_chat_{user.id}_{int(__import__('time').time())}"
        user_context = memory_service.process_user_message(
            user.id, request.prompt, session_id, 'text', db
        )

        # Create enhanced system prompt with user context
        enhanced_prompt = SYSTEM_PROMPT
        if user_context:
            enhanced_prompt += f"\n\nUSER CONTEXT:\n{user_context}"

        # Initialize Gemini service
        gemini_service = GeminiService()

        # Generate response
        ai_response = await gemini_service.generate_text_response(
            request.prompt,
            enhanced_prompt
        )

        # Store AI response in memory
        memory_service.process_ai_response(user.id, ai_response, session_id, 'text', db)

        logger.info(f"Text chat response generated: {len(ai_response)} characters")
        return ChatResponse(response=ai_response)

    except Exception as e:
        logger.error(f"Error in chat endpoint: {e}")

        # Fallback response for therapeutic context
        fallback_response = (
            "I'm experiencing a technical difficulty right now. "
            "Your feelings and thoughts are important - would you like to try again, "
            "or would it help to contact a human therapist?"
        )
        return ChatResponse(response=fallback_response)

@router.websocket("/ws")
async def websocket_text_chat(websocket: WebSocket):
    """WebSocket endpoint for text-based chat with streaming responses"""
    await websocket.accept()
    logger.info("WebSocket connection established for text chat")

    user = None
    db = None
    session_id = None

    try:
        # Get database session
        db = next(get_db())

        # First message should be authentication token
        auth_msg = await websocket.receive_text()
        logger.info(f"WebSocket received auth message: {auth_msg[:50]}...")

        if auth_msg.startswith("AUTH:"):
            token = auth_msg[5:]  # Remove "AUTH:" prefix
            logger.info(f"WebSocket extracted token: {token[:20]}...")
            try:
                from backend.services.auth_service import get_current_user
                user = get_current_user(token, db)
                if user:
                    session_id = f"ws_text_{user.id}_{int(__import__('time').time())}"
                    await websocket.send_text("AUTH_SUCCESS")
                    logger.info(f"WebSocket authenticated for user: {user.email}")
                else:
                    logger.warning("WebSocket auth failed: get_current_user returned None")
                    await websocket.send_text("AUTH_FAILED")
                    await websocket.close()
                    return
            except Exception as e:
                logger.error(f"WebSocket auth error: {e}")
                logger.error(f"WebSocket auth error type: {type(e)}")
                import traceback
                logger.error(f"WebSocket auth traceback: {traceback.format_exc()}")
                await websocket.send_text("AUTH_FAILED")
                await websocket.close()
                return
        else:
            # No auth provided, use anonymous mode
            logger.info("WebSocket running in anonymous mode")
            session_id = f"ws_anon_{int(__import__('time').time())}"

        gemini_service = GeminiService()

        while True:
            # Receive message from client
            user_msg = await websocket.receive_text()
            logger.info(f"Received WebSocket message: {user_msg[:100]}...")

            # Crisis detection keywords
            crisis_indicators = [
                "suicide", "kill myself", "self-harm", "end it all", 
                "hurt myself", "don't want to live", "better off dead"
            ]
            
            if any(indicator in user_msg.lower() for indicator in crisis_indicators):
                crisis_response = (
                    "🚨 I'm very concerned about what you're sharing. "
                    "Please reach out for immediate support:\n\n"
                    "• National Suicide Prevention Lifeline: 988\n"
                    "• Crisis Text Line: Text HOME to 741741\n"
                    "• Emergency Services: 911\n\n"
                    "Your life has value and there are people who want to help."
                )
                await websocket.send_text(crisis_response)
                continue

            # Check for goodbye
            if detect_goodbye(user_msg):
                goodbye_response = (
                    "Thank you for sharing with me today. Take care of yourself, and remember "
                    "that you have the strength to handle whatever comes your way. Feel free to "
                    "come back anytime you need support. Goodbye for now! 💙"
                )
                await websocket.send_text(goodbye_response)
                continue

            try:
                # Process user message and get memory context (if authenticated)
                enhanced_prompt = SYSTEM_PROMPT
                if user and db:
                    user_context = memory_service.process_user_message(
                        user.id, user_msg, session_id, 'text', db
                    )
                    if user_context:
                        enhanced_prompt += f"\n\nUSER CONTEXT:\n{user_context}"

                # Generate response
                response_text = await gemini_service.generate_text_response(
                    user_msg,
                    enhanced_prompt
                )

                # Clean up the response text
                if response_text:
                    # Remove common voice artifacts
                    artifacts_to_remove = [
                        "[pause]", "...", "… [pause] …", "[PAUSE]",
                        "*pause*", "(pause)", "... [pause] ...",
                        "[breath]", "*breath*", "(breath)"
                    ]
                    
                    for artifact in artifacts_to_remove:
                        response_text = response_text.replace(artifact, "")
                    
                    # Clean up extra spaces and line breaks
                    clean_text = " ".join(response_text.split())

                    # Store AI response in memory (if authenticated)
                    if user and db:
                        memory_service.process_ai_response(user.id, clean_text, session_id, 'text', db)

                    await websocket.send_text(clean_text)
                else:
                    fallback_response = "I apologize, I couldn't generate a proper response. How else can I help you?"
                    if user and db:
                        memory_service.process_ai_response(user.id, fallback_response, session_id, 'text', db)
                    await websocket.send_text(fallback_response)

            except Exception as e:
                logger.error(f"Error generating response: {e}")
                await websocket.send_text(
                    "I apologize, but I'm having technical difficulties. "
                    "Your mental health is important - please consider speaking "
                    "with a human therapist if you need immediate support."
                )

    except WebSocketDisconnect:
        logger.info("WebSocket client disconnected")
    except Exception as e:
        logger.error(f"WebSocket error: {e}")
        try:
            await websocket.send_text(f"Connection error occurred: {str(e)}")
        except:
            pass
    finally:
        # Clean up database session
        if db:
            db.close()

@router.get("/memory/test")
async def test_memory(
    user: User = Depends(get_authenticated_user),
    db: Session = Depends(get_db)
):
    """Test endpoint to check user memory"""
    try:
        context = memory_service.get_user_context(user.id, db)
        return {
            "user_id": user.id,
            "user_name": user.full_name or user.username,
            "context": context
        }
    except Exception as e:
        logger.error(f"Error testing memory: {e}")
        raise HTTPException(status_code=500, detail=str(e))
