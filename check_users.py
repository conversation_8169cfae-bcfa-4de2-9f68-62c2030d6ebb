#!/usr/bin/env python3
"""Script to check users in the database"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from backend.models.database import get_db, User
from backend.services.auth_service import AuthService
from sqlalchemy.orm import Session

def check_users():
    """Check all users in the database"""
    db = next(get_db())
    
    users = db.query(User).all()
    print(f"Found {len(users)} users in database:")
    
    for user in users:
        print(f"  ID: {user.id}")
        print(f"  Email: {user.email}")
        print(f"  Username: {user.username}")
        print(f"  Full Name: {user.full_name}")
        print(f"  Is Active: {user.is_active}")
        print(f"  Is Verified: {user.is_verified}")
        print(f"  Created: {user.created_at}")
        print(f"  Password Hash: {user.hashed_password[:50]}...")
        print("-" * 50)

def test_password_verification():
    """Test password verification for known users"""
    db = next(get_db())
    
    test_cases = [
        ("<EMAIL>", "password123"),
        ("<EMAIL>", "password123"),
    ]
    
    for email, password in test_cases:
        user = db.query(User).filter(User.email == email).first()
        if user:
            print(f"Testing password for {email}:")
            print(f"  Stored hash: {user.hashed_password[:50]}...")
            
            # Test password verification
            is_valid = AuthService.verify_password(password, user.hashed_password)
            print(f"  Password '{password}' is valid: {is_valid}")
            
            # Try some other common passwords
            for test_pwd in ["password", "123456", "admin", "test"]:
                is_valid = AuthService.verify_password(test_pwd, user.hashed_password)
                if is_valid:
                    print(f"  Password '{test_pwd}' is valid: {is_valid}")
        else:
            print(f"User {email} not found")
        print("-" * 50)

if __name__ == "__main__":
    print("=== Checking Users ===")
    check_users()
    
    print("\n=== Testing Password Verification ===")
    test_password_verification()
