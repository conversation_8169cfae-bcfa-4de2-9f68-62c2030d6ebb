"""
Gemini WebRTC handlers for AI Therapist
"""
import asyncio
import time
import logging
from typing import Literal, AsyncGenerator
import numpy as np
from google.genai.types import (
    LiveConnectConfig,
    PrebuiltVoiceConfig,
    SpeechConfig,
    VoiceConfig,
)

from backend.config.settings import get_settings, SYSTEM_PROMPT
from backend.services.webrtc_service import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, AsyncAudioVideoStreamHandler, FASTRTC_AVAILABLE
from backend.services.gemini_service import GeminiService
from backend.services.memory_service import memory_service
from backend.utils.audio_utils import encode_audio_dict, encode_image, detect_goodbye
from backend.utils.session_manager import add_session, remove_session, get_session_user
from backend.models.database import get_db

logger = logging.getLogger(__name__)

class GeminiHandler(AsyncStreamHandler):
    """Handler for Gemini voice interactions"""
    
    def __init__(
        self,
        expected_layout: Literal["mono"] = "mono",
        output_sample_rate: int = 24000,
    ) -> None:
        super().__init__(
            expected_layout,
            output_sample_rate,
            input_sample_rate=16000,
        )
        self.input_queue: asyncio.Queue = asyncio.Queue()
        self.output_queue: asyncio.Queue = asyncio.Queue()
        self.quit: asyncio.Event = asyncio.Event()
        self.session_context = {}
        self.session = None
        self.session_id = None
        self.settings = get_settings()
        
    def copy(self) -> "GeminiHandler":
        return GeminiHandler(
            expected_layout="mono",
            output_sample_rate=self.output_sample_rate,
        )

    async def start_up(self):
        if not FASTRTC_AVAILABLE:
            logger.warning("FastRTC not available, skipping voice handler setup")
            return

        try:
            # Set default voice
            voice_name = "Aoede"

            # Try to get voice from args if available
            try:
                await asyncio.wait_for(self.wait_for_args(), timeout=5.0)
                if self.latest_args and len(self.latest_args) > 1:
                    voice_name = self.latest_args[1]
            except (asyncio.TimeoutError, Exception) as args_error:
                logger.warning(f"Could not get args, using default voice: {args_error}")

            logger.info(f"Starting handler with voice: {voice_name}")

            # Initialize Gemini service
            gemini_service = GeminiService()
            self.client = gemini_service.get_client()

            # Create LiveConnect configuration
            try:
                config = LiveConnectConfig(
                    response_modalities=["AUDIO"],
                    speech_config=SpeechConfig(
                        voice_config=VoiceConfig(
                            prebuilt_voice_config=PrebuiltVoiceConfig(
                                voice_name=voice_name,
                            )
                        )
                    ),
                    generation_config={
                        "temperature": 0.8,
                        "max_output_tokens": 256,
                    },
                    system_instruction={
                        "parts": [{"text": SYSTEM_PROMPT}]
                    }
                )
                logger.info(f"LiveConnectConfig created with system instructions and voice: {voice_name}")
            except Exception as config_error:
                logger.warning(f"Failed to create config with system_instruction: {config_error}")
                # Fallback config without system_instruction
                config = LiveConnectConfig(
                    response_modalities=["AUDIO"],
                    speech_config=SpeechConfig(
                        voice_config=VoiceConfig(
                            prebuilt_voice_config=PrebuiltVoiceConfig(
                                voice_name=voice_name,
                            )
                        )
                    ),
                    generation_config={
                        "temperature": 0.8,
                        "max_output_tokens": 256,
                    }
                )
                logger.info(f"Fallback LiveConnectConfig created with voice: {voice_name}")
            
            logger.info(f"Starting Gemini Live session with voice: {voice_name}")
            
            # Start Gemini Live session
            async with self.client.aio.live.connect(
                model="gemini-2.0-flash-exp", 
                config=config
            ) as session:
                logger.info("Gemini Live session established successfully")
                self.session = session
                self.session_id = f"voice_{int(time.time())}"
                add_session(self.session_id, "voice", self)
                
                # Send identity setup messages
                try:
                    await session.send({"text": f"SYSTEM INSTRUCTIONS: {SYSTEM_PROMPT}"})
                    
                    identity_msg = (
                        f"IMPORTANT: Your identity is: AI Therapist created by Critical Future. "
                        f"Your voice name is {voice_name}. When someone asks 'who are you' or about your identity, "
                        f"always respond: 'I am an AI Therapist created by Critical Future to provide "
                        f"compassionate Therapeutic support. My voice name is {voice_name}.' "
                        f"Never say you are created by Google or Gemini. Always say Critical Future."
                    )
                    await session.send({"text": identity_msg})

                    # Add user memory context if available
                    user_id, user_name = get_session_user(self.session_id)
                    if user_id:
                        try:
                            db = next(get_db())
                            user_context = memory_service.get_user_context(user_id, db)
                            if user_context:
                                context_msg = f"USER MEMORY CONTEXT:\n{user_context}"
                                await session.send({"text": context_msg})
                                logger.info(f"User memory context sent for user {user_id}")
                            db.close()
                        except Exception as memory_error:
                            logger.warning(f"Could not load user memory: {memory_error}")

                    # Send warm-up message to reduce initial latency
                    warmup_msg = "Please respond with just 'Hello' to confirm you're ready."
                    await session.send({"text": warmup_msg})

                    logger.info("System prompt and identity reinforcement sent successfully")
                except Exception as prompt_error:
                    logger.warning(f"Could not send system messages: {prompt_error}")
                
                # Process session stream
                async for chunk in session.start_stream(
                    stream=self.stream(), 
                    mime_type="audio/pcm"
                ):
                    if self.quit.is_set():
                        logger.info("Quit signal received, breaking stream")
                        break
                        
                    if chunk.data and len(chunk.data) > 0:
                        try:
                            array = np.frombuffer(chunk.data, dtype=np.int16)
                            if not self.quit.is_set() and array is not None and array.size > 0:
                                try:
                                    self.output_queue.put_nowait((self.output_sample_rate, array))
                                except asyncio.QueueFull:
                                    logger.warning("Output queue full, dropping audio frame")
                        except Exception as audio_error:
                            logger.error(f"Error processing audio chunk: {audio_error}")
                    
                    if chunk.text:
                        logger.info(f"Gemini text response: {chunk.text[:100]}...")
                        
                        # Check for goodbye and auto-end session
                        if detect_goodbye(chunk.text):
                            logger.info("Goodbye detected, ending session")
                            await asyncio.sleep(2)  # Let the goodbye message play
                            self.quit.set()
                            break
                        
                        # Check for incorrect identity and send correction
                        if any(phrase in chunk.text.lower() for phrase in ["i am gemini", "created by google", "i'm gemini", "google ai"]):
                            correction_msg = (
                                "CORRECTION: Remember, you are an AI Therapist created by Critical Future, "
                                f"not by Google or Gemini. Your name is {voice_name}. Please correct your response."
                            )
                            try:
                                await session.send({"text": correction_msg})
                                logger.info("Sent identity correction")
                            except Exception as correction_error:
                                logger.warning(f"Could not send correction: {correction_error}")

        except Exception as e:
            logger.error(f"Error in GeminiHandler start_up: {e}")
            logger.info("Handler startup failed, will operate in degraded mode")
        finally:
            # Clean up session
            if self.session_id:
                remove_session(self.session_id)

    async def stream(self) -> AsyncGenerator[bytes, None]:
        """Stream audio data to Gemini"""
        while not self.quit.is_set():
            try:
                audio_data = await asyncio.wait_for(self.input_queue.get(), 0.1)
                yield audio_data
            except (asyncio.TimeoutError, TimeoutError):
                pass
            except Exception as e:
                logger.error(f"Error in audio streaming: {e}")
                break

    async def receive(self, frame: tuple[int, np.ndarray]) -> None:
        """Receive audio from client and queue for Gemini"""
        try:
            if self.quit.is_set():
                return

            if frame is None or len(frame) != 2:
                return

            _, array = frame
            if array is None:
                return

            array = array.squeeze()

            if array is None or array.size == 0:
                return

            audio_bytes = array.astype(np.int16).tobytes()

            if not self.quit.is_set() and len(audio_bytes) > 0:
                try:
                    self.input_queue.put_nowait(audio_bytes)
                except asyncio.QueueFull:
                    logger.warning("Input queue full, dropping audio frame")

        except Exception as e:
            logger.error(f"Error processing incoming audio: {e}")

    async def emit(self) -> tuple[int, np.ndarray] | None:
        """Emit audio response from Gemini to client"""
        try:
            if self.quit.is_set():
                return None
            return await asyncio.wait_for(self.output_queue.get(), timeout=0.1)
        except asyncio.TimeoutError:
            return None
        except Exception as e:
            logger.error(f"Error emitting audio: {e}")
            return None

    def shutdown(self) -> None:
        """Clean shutdown of the handler"""
        logger.info("Shutting down Gemini handler")
        self.quit.set()
        
        if self.session_id:
            remove_session(self.session_id)
        
        # Clear queues
        while not self.input_queue.empty():
            try:
                self.input_queue.get_nowait()
            except:
                break
                
        while not self.output_queue.empty():
            try:
                self.output_queue.get_nowait()
            except:
                break
