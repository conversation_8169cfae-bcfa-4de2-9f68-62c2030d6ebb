# 📞 Phone Mode Setup for AI Therapist

This guide explains how to set up and run the AI Therapist in phone mode with dynamic phone number and access code management.

## 🎯 Overview

The AI Therapist supports phone calls through FastPhone integration. The phone number and access code are dynamically generated each time the service starts, and the application automatically captures and updates these credentials.

## 🚀 Quick Start

### Option 1: Hybrid Mode (Recommended) ⭐

```bash
python run_phone_hybrid.py
```

This provides:
- 📞 **Phone Service**: FastPhone on port 8000 with live phone number
- 🌐 **Web Interface**: Full web app on port 8001 with all features
- 🔄 **Auto-sync**: Phone credentials automatically update in web interface

### Option 2: Direct Phone Mode Only

```bash
python run_phone_direct.py
```

This provides:
- 📞 **Phone Service Only**: FastPhone on port 8000
- 📋 **Console Output**: Phone credentials displayed in terminal
- 🎯 **Minimal**: No web interface, phone calls only

### Option 3: Web Mode Only (Default)

```bash
python run.py
```

This provides:
- 🌐 **Web Interface**: Full web app on port 8000
- 💬 **Text/Voice/Video**: All chat modes except phone calls
- 📱 **Static Phone Info**: Shows placeholder phone number

## 📱 How It Works

### 1. Dynamic Credential Capture

When the application starts in phone mode:
- FastPhone service generates a new phone number and access code
- The phone monitor captures these credentials from the console output
- Credentials are automatically updated in the backend
- The web interface refreshes every 5 seconds to show new credentials

### 2. Credential Patterns

The system recognizes these patterns in the console output:
- `Phone: +1 ************ Access Code: 163760`
- `FastPhone service started: +1-************ with access code 163760`
- `Call ****** 713 4471 and use code 163760`
- `Number: +1-************ Code: 163760`
- `+1 ************ 163760`

### 3. Web Interface Updates

- Phone number and access code flash green when updated
- Toast notifications show when credentials change
- Auto-refresh every 5 seconds ensures latest information
- Call logs are tracked and displayed in real-time

## 🔧 Configuration

### Environment Variables

- `MODE=PHONE` - Enables phone mode
- `GEMINI_API_KEY` - Your Google Gemini API key
- `PORT` - Server port (default: 8000)

### Phone Service Settings

The phone service can be configured in `backend/services/phone_service.py`:

```python
# Phone stream configuration
self.phone_stream = Stream(
    modality="audio",
    mode="send-receive",
    handler=PhoneHandler(),
    concurrency_limit=10,  # Max concurrent calls
    time_limit=1800,       # 30 minutes max per call
)
```

## 📊 API Endpoints

### Get Phone Configuration
```http
GET /phone/config
```

Returns current phone number, access code, and call logs.

### Update Phone Credentials
```http
POST /phone/update-credentials?phone_number=+1234567890&access_code=123456
```

Manually update phone credentials.

### Handle Incoming Call
```http
POST /phone/incoming
```

Log an incoming phone call.

## 🛠️ Testing

### Test Credential Extraction
```bash
python test_phone_credentials.py
```

This script tests the phone credential extraction patterns with sample data.

### Simulate Incoming Call
Use the web interface "Simulate Call" button or call the API directly.

## 📋 Troubleshooting

### Common Issues

1. **Credentials Not Updating**
   - Check console output for FastPhone startup messages
   - Verify the phone monitor is running
   - Check network connectivity

2. **Phone Calls Not Working**
   - Ensure FastRTC is properly installed
   - Check Gemini API key is valid
   - Verify phone handler is initialized

3. **Access Code Changes Too Frequently**
   - This is normal - FastPhone generates new codes each session
   - The web interface automatically updates to show current codes
   - Users should check the web interface for the latest access code

### Debug Mode

Enable debug logging:
```python
import logging
logging.basicConfig(level=logging.DEBUG)
```

### Manual Credential Update

If automatic detection fails, you can manually update credentials via the API:

```bash
curl -X POST "http://localhost:8000/phone/update-credentials?phone_number=%2B1234567890&access_code=123456"
```

## 🔄 Workflow

### For Hybrid Mode (Recommended):

1. **Start Services**: Run `python run_phone_hybrid.py`
2. **Check Console**: Watch for phone credentials in terminal output
3. **Open Web Interface**: Visit `http://localhost:8001`
4. **Go to Phone Tab**: See live phone number and access code
5. **Share Credentials**: Give callers the current phone number and access code
6. **Make/Receive Calls**: Calls are automatically routed to the AI Therapist
7. **Monitor Activity**: View call history and status in real-time

### For Direct Phone Mode:

1. **Start Phone Service**: Run `python run_phone_direct.py`
2. **Note Credentials**: Copy phone number and access code from console
3. **Share with Callers**: Provide the phone number and access code
4. **Handle Calls**: All calls go directly to AI Therapist

### Current Live Example:

```
📞 Phone Number: +1 ************
🔑 Access Code: 376423
⏰ Quota: 30:00 minutes remaining
🌐 Web Interface: http://localhost:8001
```

## 📈 Production Deployment

For production use:

1. **Use Process Manager**: Deploy with PM2, systemd, or similar
2. **Log Management**: Set up proper logging and log rotation
3. **Monitoring**: Monitor phone service health and credential updates
4. **Backup**: Implement call log backup and recovery
5. **Security**: Secure API endpoints and implement rate limiting

## 🎉 Success Indicators

When everything is working correctly, you should see:

- ✅ Console shows "Phone service initialized" with credentials
- ✅ Web interface displays current phone number and access code
- ✅ Credentials flash green when updated
- ✅ Call logs appear when calls are made
- ✅ Toast notifications show credential updates

## 📞 Making Test Calls

1. Check the web interface for current phone number and access code
2. Call the displayed phone number
3. When prompted, enter the access code
4. Start talking to your AI Therapist!
5. Check the call logs in the web interface

The phone service is now ready for production use with dynamic credential management!
