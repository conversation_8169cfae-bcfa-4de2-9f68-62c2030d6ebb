"""
Payment routes for AI Therapist
"""
import stripe
import logging
from fastapi import APIRouter, Depends, HTTPException, status, Request
from sqlalchemy.orm import Session
from pydantic import BaseModel
from typing import Dict, Any

from backend.models.database import get_db, SUBSCRIPTION_PLANS
from backend.services.stripe_service import StripeService, STRIPE_WEBHOOK_SECRET
from backend.routes.auth import get_authenticated_user
from backend.models.database import User

router = APIRouter(prefix="/payments", tags=["Payments"])
logger = logging.getLogger(__name__)

# Request models
class CreateCheckoutRequest(BaseModel):
    plan_type: str
    success_url: str
    cancel_url: str

class CreatePortalRequest(BaseModel):
    return_url: str

@router.get("/plans")
async def get_subscription_plans():
    """Get available subscription plans"""
    return {
        "plans": SUBSCRIPTION_PLANS
    }

@router.post("/create-checkout-session")
async def create_checkout_session(
    request: CreateCheckoutRequest,
    user: User = Depends(get_authenticated_user),
    db: Session = Depends(get_db)
):
    """Create a Stripe checkout session for subscription"""
    try:
        session_data = StripeService.create_checkout_session(
            db=db,
            user=user,
            plan_type=request.plan_type,
            success_url=request.success_url,
            cancel_url=request.cancel_url
        )
        
        return session_data
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error creating checkout session: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to create checkout session"
        )

@router.post("/create-portal-session")
async def create_portal_session(
    request: CreatePortalRequest,
    user: User = Depends(get_authenticated_user),
    db: Session = Depends(get_db)
):
    """Create a Stripe customer portal session"""
    try:
        portal_url = StripeService.create_portal_session(
            user=user,
            return_url=request.return_url
        )
        
        return {"portal_url": portal_url}
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error creating portal session: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to create portal session"
        )

@router.get("/usage")
async def get_usage_summary(
    user: User = Depends(get_authenticated_user),
    db: Session = Depends(get_db)
):
    """Get user's usage summary"""
    try:
        usage_data = StripeService.get_usage_summary(db=db, user=user)
        return usage_data
        
    except Exception as e:
        logger.error(f"Error getting usage summary: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to get usage summary"
        )

@router.post("/cancel-subscription")
async def cancel_subscription(
    user: User = Depends(get_authenticated_user),
    db: Session = Depends(get_db)
):
    """Cancel user's subscription"""
    try:
        success = StripeService.cancel_subscription(db=db, user=user)
        
        if success:
            return {"message": "Subscription cancelled successfully"}
        else:
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail="Failed to cancel subscription"
            )
            
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error cancelling subscription: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to cancel subscription"
        )

@router.post("/webhook")
async def stripe_webhook(request: Request, db: Session = Depends(get_db)):
    """Handle Stripe webhooks"""
    payload = await request.body()
    sig_header = request.headers.get('stripe-signature')
    
    try:
        event = stripe.Webhook.construct_event(
            payload, sig_header, STRIPE_WEBHOOK_SECRET
        )
    except ValueError as e:
        logger.error(f"Invalid payload: {e}")
        raise HTTPException(status_code=400, detail="Invalid payload")
    except stripe.error.SignatureVerificationError as e:
        logger.error(f"Invalid signature: {e}")
        raise HTTPException(status_code=400, detail="Invalid signature")
    
    # Handle the event
    if event['type'] == 'checkout.session.completed':
        session = event['data']['object']
        success = StripeService.handle_checkout_completed(db, session)
        if not success:
            logger.error(f"Failed to handle checkout completion: {session['id']}")
    
    elif event['type'] == 'customer.subscription.updated':
        subscription = event['data']['object']
        success = StripeService.handle_subscription_updated(db, subscription)
        if not success:
            logger.error(f"Failed to handle subscription update: {subscription['id']}")
    
    elif event['type'] == 'customer.subscription.deleted':
        subscription = event['data']['object']
        # Handle subscription cancellation
        logger.info(f"Subscription deleted: {subscription['id']}")
    
    elif event['type'] == 'invoice.payment_succeeded':
        invoice = event['data']['object']
        # Handle successful payment
        logger.info(f"Payment succeeded: {invoice['id']}")
    
    elif event['type'] == 'invoice.payment_failed':
        invoice = event['data']['object']
        # Handle failed payment
        logger.warning(f"Payment failed: {invoice['id']}")
    
    else:
        logger.info(f"Unhandled event type: {event['type']}")
    
    return {"status": "success"}

@router.get("/config")
async def get_payment_config():
    """Get payment configuration for frontend"""
    import os
    return {
        "stripe_publishable_key": os.getenv("STRIPE_PUBLISHABLE_KEY"),
        "plans": SUBSCRIPTION_PLANS
    }
