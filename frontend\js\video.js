/**
 * Video Chat Functionality for AI Therapist
 */

function setupVideoControls() {
    if (!startVideoButton) return;

    startVideoButton.addEventListener('click', () => {
        if (videoPeerConnection && videoPeerConnection.connectionState === 'connected') {
            stopVideoWebRTC();
        } else {
            setupVideoWebRTC();
        }
    });

    if (toggleCameraBtn) {
        toggleCameraBtn.addEventListener('click', toggleCamera);
    }

    if (toggleMicBtn) {
        toggleMicBtn.addEventListener('click', toggleVideoMic);
    }
}

function initializeVideoMode() {
    // Initialize camera preview
    initializeCameraPreview();
}

async function initializeCameraPreview() {
    if (!localVideo) return;

    try {
        const stream = await navigator.mediaDevices.getUserMedia({
            video: {
                width: { ideal: 640 },
                height: { ideal: 480 },
                facingMode: 'user'
            },
            audio: false // Just for preview
        });
        
        localVideo.srcObject = stream;
        localVideo.play();
        
        // Store for later use
        localStream = stream;
        
    } catch (error) {
        console.error('Error accessing camera:', error);
        if (localStatus) {
            localStatus.textContent = 'Camera not available';
        }
    }
}

function updateVideoButtonState() {
    if (!startVideoButton) return;

    if (videoPeerConnection && videoPeerConnection.connectionState === 'connected') {
        startVideoButton.innerHTML = '🔴 End Video Session';
        startVideoButton.classList.add('danger');
        startVideoButton.disabled = false;
        if (toggleCameraBtn) toggleCameraBtn.style.display = 'inline-flex';
        if (toggleMicBtn) toggleMicBtn.style.display = 'inline-flex';
    } else if (videoPeerConnection && (videoPeerConnection.connectionState === 'connecting' || videoPeerConnection.connectionState === 'new')) {
        startVideoButton.innerHTML = `
            <div class="spinner"></div>
            <span>Connecting...</span>
        `;
        startVideoButton.disabled = true;
        startVideoButton.classList.remove('danger');
    } else {
        startVideoButton.innerHTML = '📹 Start Video Session';
        startVideoButton.classList.remove('danger');
        startVideoButton.disabled = false;
        if (toggleCameraBtn) toggleCameraBtn.style.display = 'none';
        if (toggleMicBtn) toggleMicBtn.style.display = 'none';
    }
}

// Video WebRTC setup
async function setupVideoWebRTC() {
    const config = RTC_CONFIGURATION;
    videoPeerConnection = new RTCPeerConnection(config);
    video_webrtc_id = Math.random().toString(36).substring(7);

    try {
        const stream = await navigator.mediaDevices.getUserMedia({ 
            video: {
                width: { ideal: 640 },
                height: { ideal: 480 },
                facingMode: 'user'
            },
            audio: {
                echoCancellation: true,
                noiseSuppression: true,
                autoGainControl: true,
                sampleRate: 16000
            } 
        });
        
        localStream = stream;
        if (localVideo) {
            localVideo.srcObject = stream;
        }
        
        stream.getTracks().forEach(track => videoPeerConnection.addTrack(track, stream));

        videoPeerConnection.addEventListener('connectionstatechange', () => {
            console.log('Video connection state:', videoPeerConnection.connectionState);
            if (videoPeerConnection.connectionState === 'connected') {
                showToast('Connected to AI Therapist Video!', 'success');
                showStatus('Video session active - say "bye" to end');
                if (localStatus) localStatus.textContent = 'Connected';
            } else if (['disconnected', 'failed', 'closed'].includes(videoPeerConnection.connectionState)) {
                showStatus('Video session ended');
                if (localStatus) localStatus.textContent = 'Camera Ready';
                endVideoSession();
            }
            updateVideoButtonState();
        });

        videoPeerConnection.onicecandidate = ({ candidate }) => {
            if (candidate) {
                fetch('/video/webrtc/offer', {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify({
                        candidate: candidate.toJSON(),
                        webrtc_id: video_webrtc_id,
                        type: "ice-candidate",
                    })
                });
            }
        };

        // Audio from AI (no video from AI)
        videoPeerConnection.addEventListener('track', (evt) => {
            if (evt.track.kind === 'audio') {
                const audioElement = new Audio();
                audioElement.srcObject = evt.streams[0];
                audioElement.play().catch(e => console.error("Video audio play failed:", e));
            }
        });

        videoDataChannel = videoPeerConnection.createDataChannel('control');
        videoDataChannel.onmessage = (event) => {
            const eventJson = JSON.parse(event.data);
            if (eventJson.type === "error") {
                showToast(eventJson.message, 'error');
            } else if (eventJson.type === "send_input") {
                fetch('/input_hook', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        webrtc_id: video_webrtc_id,
                        voice_name: videoVoiceSelect ? videoVoiceSelect.value : 'Aoede',
                        mode: "video",
                        uploaded_files: uploadedFiles
                    })
                });
            }
        };

        const offer = await videoPeerConnection.createOffer();
        await videoPeerConnection.setLocalDescription(offer);

        const response = await fetch('/video/webrtc/offer', {
            method: 'POST',
            headers: { 'Content-Type': 'application/json' },
            body: JSON.stringify({
                sdp: videoPeerConnection.localDescription.sdp,
                type: videoPeerConnection.localDescription.type,
                webrtc_id: video_webrtc_id,
            })
        });

        const serverResponse = await response.json();

        if (serverResponse.status === 'failed') {
            const errorMsg = serverResponse.meta.error === 'concurrency_limit_reached'
                ? `Too many video sessions. Maximum limit is ${serverResponse.meta.limit}. Please try again later.`
                : `Video connection failed: ${serverResponse.meta.error}`;
            showToast(errorMsg, 'error');
            stopVideoWebRTC();
            return;
        }

        await videoPeerConnection.setRemoteDescription(serverResponse);

    } catch (err) {
        console.error('Error setting up Video WebRTC:', err);
        showToast('Failed to establish video connection. Please check your camera and microphone permissions.', 'error');
        stopVideoWebRTC();
    }
}

function stopVideoWebRTC() {
    if (videoPeerConnection) {
        videoPeerConnection.close();
        videoPeerConnection = null;
    }
    
    updateVideoButtonState();
}

function endVideoSession() {
    stopVideoWebRTC();
    showStatus('Video session ended');
}

function toggleCamera() {
    if (!localStream) return;

    const videoTrack = localStream.getVideoTracks()[0];
    if (videoTrack) {
        isCameraOn = !isCameraOn;
        videoTrack.enabled = isCameraOn;
        
        if (toggleCameraBtn) {
            toggleCameraBtn.innerHTML = isCameraOn ? '📷 Turn Off Camera' : '📷 Turn On Camera';
        }
        
        if (localStatus) {
            localStatus.textContent = isCameraOn ? 'Camera On' : 'Camera Off';
        }
    }
}

function toggleVideoMic() {
    if (!localStream) return;

    const audioTrack = localStream.getAudioTracks()[0];
    if (audioTrack) {
        const isEnabled = audioTrack.enabled;
        audioTrack.enabled = !isEnabled;
        
        if (toggleMicBtn) {
            toggleMicBtn.innerHTML = audioTrack.enabled ? '🎤 Mute Mic' : '🎤 Unmute Mic';
        }
    }
}
