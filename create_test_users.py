#!/usr/bin/env python3
"""Script to create test users for authentication testing"""

import requests
import json

BASE_URL = "http://localhost:8000"

def create_test_user(email, username, password, full_name=None):
    """Create a test user via registration endpoint"""
    print(f"👤 Creating test user: {email}")
    
    response = requests.post(f"{BASE_URL}/auth/register", json={
        "email": email,
        "username": username,
        "password": password,
        "full_name": full_name
    })
    
    if response.status_code == 200:
        data = response.json()
        print(f"✅ User created successfully!")
        print(f"   User ID: {data.get('id', 'N/A')}")
        print(f"   Email: {data.get('email', 'N/A')}")
        print(f"   Username: {data.get('username', 'N/A')}")
        return True
    else:
        print(f"❌ User creation failed: {response.status_code}")
        try:
            error_data = response.json()
            print(f"   Error: {error_data}")
        except:
            print(f"   Error: {response.text}")
        return False

if __name__ == "__main__":
    # Create test users
    test_users = [
        ("<EMAIL>", "testuser", "password123", "Test User"),
        ("<EMAIL>", "omara", "password123", "Omar A"),
        ("<EMAIL>", "test4218", "password123", "Test User 4218"),
    ]
    
    print("=== Creating Test Users ===")
    for email, username, password, full_name in test_users:
        create_test_user(email, username, password, full_name)
        print("-" * 50)
